import { NextRequest, NextResponse } from 'next/server';
import { validateInput, sanitizeInput, generateCSRFToken, validateCSRFToken, rateLimit, setSecurityHeaders } from '@/lib/security';
import { getApiUrl } from '@/app/config/api';

// Rate limiting configuration
const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many login attempts, please try again later'
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await loginRateLimit(request);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: rateLimitResult.message },
        { status: 429, headers: setSecurityHeaders() }
      );
    }

    // Validate CSRF token
    const csrfToken = request.headers.get('x-csrf-token');
    if (!csrfToken || !validateCSRFToken(csrfToken)) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Parse and validate request body
    const formData = await request.formData();
    const email = formData.get('username') as string;
    const password = formData.get('password') as string;

    // Input validation
    const emailValidation = validateInput(email, {
      required: true,
      type: 'email',
      maxLength: 254
    });

    const passwordValidation = validateInput(password, {
      required: true,
      minLength: 6,
      maxLength: 128
    });

    if (!emailValidation.isValid) {
      return NextResponse.json(
        { error: emailValidation.error },
        { status: 400, headers: setSecurityHeaders() }
      );
    }

    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.error },
        { status: 400, headers: setSecurityHeaders() }
      );
    }

    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email);
    const sanitizedPassword = sanitizeInput(password);

    // Forward request to backend API with additional security headers
    const backendResponse = await fetch(`${getApiUrl()}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': request.headers.get('user-agent') || '',
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
        'X-CSRF-Token': csrfToken
      },
      body: new URLSearchParams({
        username: sanitizedEmail,
        password: sanitizedPassword
      })
    });

    if (backendResponse.ok) {
      const data = await backendResponse.json();
      
      // Generate new CSRF token for next request
      const newCSRFToken = generateCSRFToken();
      
      // Create secure response with security headers
      const response = NextResponse.json(data, {
        status: 200,
        headers: {
          ...setSecurityHeaders(),
          'Set-Cookie': `csrf_token=${newCSRFToken}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=3600`,
          'X-CSRF-Token': newCSRFToken
        }
      });

      return response;
    } else {
      const errorData = await backendResponse.json();
      
      // Log failed login attempt for security monitoring
      console.warn(`Failed login attempt for email: ${sanitizedEmail}`, {
        timestamp: new Date().toISOString(),
        ip: request.headers.get('x-forwarded-for') || request.ip,
        userAgent: request.headers.get('user-agent'),
        status: backendResponse.status
      });

      return NextResponse.json(
        { error: errorData.detail || 'Authentication failed' },
        { status: backendResponse.status, headers: setSecurityHeaders() }
      );
    }
  } catch (error) {
    console.error('Login API error:', error);
    
    // Log security event
    console.error('Security event: Login API error', {
      timestamp: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || request.ip,
      userAgent: request.headers.get('user-agent'),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: setSecurityHeaders() }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...setSecurityHeaders(),
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Max-Age': '86400'
    }
  });
} 