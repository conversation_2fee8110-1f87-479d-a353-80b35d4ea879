import { NextRequest, NextResponse } from 'next/server';
import { getBaseUrl } from '@/app/config/env';
import { 
  validateRequest, 
  RateLimiter, 
  logSecurityEvent,
  getSecurityHeaders,
  sanitizeSQLInput,
  InputValidator 
} from '@/app/lib/security';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'GET');
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'POST');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PUT');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'DELETE');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleProxyRequest(request, params.path, 'PATCH');
}

async function handleProxyRequest(
  request: NextRequest,
  pathSegments: string[],
  method: string
) {
  try {
    // Security: Validate request
    const requestValidation = validateRequest(request);
    if (!requestValidation.valid) {
      logSecurityEvent('Proxy request blocked', { reason: requestValidation.reason, path: pathSegments.join('/') }, 'high');
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Security: Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimit = RateLimiter.checkRateLimit(`proxy:${clientIP}`);
    
    if (!rateLimit.allowed) {
      logSecurityEvent('Proxy rate limit exceeded', { clientIP, path: pathSegments.join('/') }, 'medium');
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString()
        }
      });
    }

    // Security: Validate and sanitize path segments
    const sanitizedPathSegments = pathSegments.map(segment => {
      const sanitized = sanitizeSQLInput(segment);
      if (sanitized !== segment) {
        logSecurityEvent('Path injection attempt detected', { 
          original: segment, 
          sanitized, 
          clientIP 
        }, 'high');
      }
      return sanitized;
    });

    const realBackendUrl = getBaseUrl();
    const targetPath = sanitizedPathSegments.join('/');
    const targetUrl = `${realBackendUrl}${targetPath}`;

    // Security: Validate target URL
    if (!targetUrl.startsWith(realBackendUrl)) {
      logSecurityEvent('Invalid proxy target URL', { targetUrl, clientIP }, 'high');
      return new NextResponse('Forbidden', { status: 403 });
    }

    // Create new headers without problematic ones
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      // Skip headers that can cause issues or security risks
      if (!['host', 'connection', 'content-length', 'x-forwarded-host', 'x-real-ip'].includes(lowerKey)) {
        // Security: Sanitize header values
        const sanitizedValue = InputValidator.sanitizeString(value);
        headers.set(key, sanitizedValue);
      }
    });

    // For non-GET requests, handle body properly
    let body: any = undefined;
    if (method !== 'GET') {
      const contentType = request.headers.get('content-type');
      
      if (contentType?.includes('multipart/form-data')) {
        // For FormData, we need to handle it specially
        const formData = await request.formData();
        
        // Security: Validate and sanitize form data
        const sanitizedFormData = new FormData();
        for (const [key, value] of formData.entries()) {
          if (typeof value === 'string') {
            const sanitizedKey = sanitizeSQLInput(key);
            const sanitizedValue = InputValidator.sanitizeString(value);
            sanitizedFormData.append(sanitizedKey, sanitizedValue);
          } else if (value instanceof File) {
            // Security: Validate file upload
            const fileValidation = InputValidator.validateFileUpload(value);
            if (!fileValidation.isValid) {
              logSecurityEvent('Invalid file upload attempt', { 
                fileName: value.name, 
                fileType: value.type, 
                fileSize: value.size,
                clientIP 
              }, 'medium');
              return NextResponse.json(
                { error: fileValidation.error },
                { status: 400 }
              );
            }
            sanitizedFormData.append(key, value);
          }
        }
        body = sanitizedFormData;
        // Remove content-type header for FormData to let browser set it
        headers.delete('content-type');
      } else if (contentType?.includes('application/json')) {
        // For JSON, read and sanitize
        const jsonData = await request.json();
        const sanitizedData = InputValidator.sanitizeObject(jsonData);
        body = JSON.stringify(sanitizedData);
        headers.set('content-type', 'application/json');
      } else {
        // For other types, read as text and sanitize
        const textData = await request.text();
        const sanitizedText = InputValidator.sanitizeString(textData);
        body = sanitizedText;
        if (contentType) {
          headers.set('content-type', contentType);
        }
      }
    }

    // Make the request to backend
    const response = await fetch(targetUrl, {
      method,
      headers,
      body,
    });

    // Get response data
    const responseText = await response.text();
    
    // Security: Sanitize response data if it's JSON
    let sanitizedResponseText = responseText;
    try {
      const jsonData = JSON.parse(responseText);
      const sanitizedData = InputValidator.sanitizeObject(jsonData);
      sanitizedResponseText = JSON.stringify(sanitizedData);
    } catch {
      // Not JSON, sanitize as text
      sanitizedResponseText = InputValidator.sanitizeString(responseText);
    }
    
    // Create response
    const proxyResponse = new NextResponse(sanitizedResponseText, {
      status: response.status,
      statusText: response.statusText,
    });

    // Copy response headers (with sanitization)
    response.headers.forEach((value, key) => {
      const lowerKey = key.toLowerCase();
      if (!['content-encoding', 'transfer-encoding', 'content-length'].includes(lowerKey)) {
        const sanitizedValue = InputValidator.sanitizeString(value);
        proxyResponse.headers.set(key, sanitizedValue);
      }
    });

    // Add security headers
    const securityHeaders = getSecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      proxyResponse.headers.set(key, value);
    });

    // Add rate limit headers
    proxyResponse.headers.set('X-RateLimit-Limit', '100');
    proxyResponse.headers.set('X-RateLimit-Remaining', rateLimit.remainingAttempts.toString());
    proxyResponse.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());

    logSecurityEvent('Proxy request successful', { 
      path: targetPath, 
      method, 
      status: response.status,
      clientIP 
    }, 'low');

    return proxyResponse;

  } catch (error) {
    logSecurityEvent('Proxy error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      path: pathSegments.join('/'),
      method 
    }, 'high');
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 