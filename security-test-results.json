{"passed": 11, "failed": 27, "tests": [{"status": "PASS", "test": "Security Header: x-content-type-options", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.495Z"}, {"status": "PASS", "test": "Security Header: x-frame-options", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.499Z"}, {"status": "PASS", "test": "Security Header: x-xss-protection", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.500Z"}, {"status": "PASS", "test": "Security Header: strict-transport-security", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.500Z"}, {"status": "PASS", "test": "Security Header: content-security-policy", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.501Z"}, {"status": "PASS", "test": "Security Header: referrer-policy", "message": "Header is present", "timestamp": "2025-06-27T13:01:02.501Z"}, {"status": "PASS", "test": "CSRF Protection", "message": "Request without token blocked", "timestamp": "2025-06-27T13:01:05.398Z"}, {"status": "PASS", "test": "CSRF Protection", "message": "Request with invalid token blocked", "timestamp": "2025-06-27T13:01:05.630Z"}, {"status": "FAIL", "test": "Rate Limiting", "message": "No requests were rate limited", "timestamp": "2025-06-27T13:01:06.599Z"}, {"status": "FAIL", "test": "Input Validation: SQL Injection", "message": "Malicious input should be blocked", "timestamp": "2025-06-27T13:01:09.150Z"}, {"status": "FAIL", "test": "Input Validation: XSS Attack", "message": "Malicious input should be blocked", "timestamp": "2025-06-27T13:01:09.260Z"}, {"status": "FAIL", "test": "Input Validation: Path Traversal", "message": "Malicious input should be blocked", "timestamp": "2025-06-27T13:01:09.388Z"}, {"status": "PASS", "test": "Input Validation: Valid Input", "message": "Valid input accepted", "timestamp": "2025-06-27T13:01:09.500Z"}, {"status": "FAIL", "test": "Authentication", "message": "Protected endpoint should require authentication", "timestamp": "2025-06-27T13:01:13.685Z"}, {"status": "FAIL", "test": "XSS Protection: <script>alert(\"xss\")...", "message": "XSS payload should be blocked", "timestamp": "2025-06-27T13:01:13.868Z"}, {"status": "FAIL", "test": "XSS Protection: javascript:alert(\"xs...", "message": "XSS payload should be blocked", "timestamp": "2025-06-27T13:01:14.134Z"}, {"status": "FAIL", "test": "XSS Protection: <img src=\"x\" onerror...", "message": "XSS payload should be blocked", "timestamp": "2025-06-27T13:01:14.367Z"}, {"status": "FAIL", "test": "XSS Protection: <iframe src=\"javascr...", "message": "XSS payload should be blocked", "timestamp": "2025-06-27T13:01:14.533Z"}, {"status": "FAIL", "test": "XSS Protection: <svg onload=\"alert('...", "message": "XSS payload should be blocked", "timestamp": "2025-06-27T13:01:14.645Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' OR '1'='1...", "message": "SQL injection should be blocked", "timestamp": "2025-06-27T13:01:14.738Z"}, {"status": "FAIL", "test": "SQL Injection Protection: '; DROP TABLE users;...", "message": "SQL injection should be blocked", "timestamp": "2025-06-27T13:01:14.855Z"}, {"status": "FAIL", "test": "SQL Injection Protection: ' UNION SELECT * FRO...", "message": "SQL injection should be blocked", "timestamp": "2025-06-27T13:01:14.978Z"}, {"status": "FAIL", "test": "SQL Injection Protection: admin'--...", "message": "SQL injection should be blocked", "timestamp": "2025-06-27T13:01:15.085Z"}, {"status": "FAIL", "test": "SQL Injection Protection: 1' OR '1' = '1' --...", "message": "SQL injection should be blocked", "timestamp": "2025-06-27T13:01:15.205Z"}, {"status": "FAIL", "test": "File Upload Security: test.php", "message": "Malicious file should be blocked", "timestamp": "2025-06-27T13:01:15.300Z"}, {"status": "FAIL", "test": "File Upload Security: test.js", "message": "Malicious file should be blocked", "timestamp": "2025-06-27T13:01:15.369Z"}, {"status": "FAIL", "test": "File Upload Security: test.exe", "message": "Malicious file should be blocked", "timestamp": "2025-06-27T13:01:15.434Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ../../../etc/passwd...", "message": "Path traversal should be blocked", "timestamp": "2025-06-27T13:01:15.491Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ..\\..\\..\\windows\\sys...", "message": "Path traversal should be blocked", "timestamp": "2025-06-27T13:01:15.535Z"}, {"status": "FAIL", "test": "Path Traversal Protection: %2e%2e%2f%2e%2e%2f%2...", "message": "Path traversal should be blocked", "timestamp": "2025-06-27T13:01:15.627Z"}, {"status": "FAIL", "test": "Path Traversal Protection: ....//....//....//et...", "message": "Path traversal should be blocked", "timestamp": "2025-06-27T13:01:15.679Z"}, {"status": "PASS", "test": "Content Security Policy", "message": "CSP properly configured", "timestamp": "2025-06-27T13:01:16.193Z"}, {"status": "PASS", "test": "Secure Storage", "message": "Secure storage module exists", "timestamp": "2025-06-27T13:01:16.194Z"}, {"status": "FAIL", "test": "API Endpoint: /api/auth/login", "message": "Unexpected status: 500", "timestamp": "2025-06-27T13:01:16.279Z"}, {"status": "FAIL", "test": "API Endpoint: /api/auth/signup", "message": "Unexpected status: 500", "timestamp": "2025-06-27T13:01:16.368Z"}, {"status": "FAIL", "test": "API Endpoint: /api/proxy/test", "message": "Unexpected status: 500", "timestamp": "2025-06-27T13:01:18.109Z"}, {"status": "FAIL", "test": "API Endpoint: /api/security/metrics", "message": "Unexpected status: 500", "timestamp": "2025-06-27T13:01:18.282Z"}, {"status": "FAIL", "test": "API Endpoint: /api/security/events", "message": "Unexpected status: 500", "timestamp": "2025-06-27T13:01:19.134Z"}]}