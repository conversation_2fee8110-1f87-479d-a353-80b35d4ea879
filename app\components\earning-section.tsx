import { Gift, Users, TrendingUp, Award, ArrowRight, Star } from 'lucide-react';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function EarningSection() {
  return (
    <section className="py-24 bg-gradient-to-b from-[#0A0F1C] via-[#151B2C] to-[#0A0F1C] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-[#FF6B2C] to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-tl from-[#4A90E2] to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-[#FF6B2C] to-[#4A90E2] rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="flex items-center gap-1 bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] px-4 py-2 rounded-full">
              <TrendingUp className="w-4 h-4 text-white" />
              <span className="text-white text-sm font-semibold">MULTIPLE INCOME STREAMS</span>
            </div>
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-[#E8F4FD] to-white bg-clip-text text-transparent">
            <span className="text-2xl sm:text-4xl md:text-6xl">Earn More with Horizon</span>
          </h2>
          
          <p className="text-[#8B9BB8] text-sm sm:text-base md:text-xl max-w-3xl mx-auto leading-relaxed">
            Unlock multiple revenue streams through our comprehensive earning programs designed to maximize your trading success
          </p>
        </motion.div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Giveaway Card */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            whileHover={{ y: -8 }}
            className="group relative"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-500"></div>
            <div className="relative bg-gradient-to-br from-[#1A1F2E] to-[#2A2F3E] rounded-3xl p-8 border border-[#3A3F4E] shadow-2xl hover:border-[#FF6B2C] transition-all duration-500">
              {/* Icon Section */}
              <div className="flex items-center justify-between mb-6">
                <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] p-4 rounded-2xl">
                  <Gift className="w-8 h-8 text-white" />
                </div>
                <div className="flex items-center gap-1 bg-[#FF6B2C]/10 px-3 py-1 rounded-full">
                  <Star className="w-3 h-3 text-[#FF6B2C]" />
                  <span className="text-[#FF6B2C] text-xs font-semibold">POPULAR</span>
                </div>
              </div>

              {/* Content */}
              <h3 className="text-2xl md:text-3xl font-bold mb-4 text-white">Giveaway Program</h3>
              <p className="text-[#8B9BB8] text-sm sm:text-base leading-relaxed mb-6">
                Participate in our exclusive trading giveaways and competitions. Win funded accounts, cash prizes, and premium trading tools. Regular events with increasing rewards for active traders.
              </p>

              {/* Features List */}
              <div className="space-y-3 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#FF6B2C] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Weekly & Monthly Competitions</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#FF6B2C] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Funded Account Rewards</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#FF6B2C] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Exclusive Trading Tools</span>
                </div>
              </div>

              {/* CTA Button */}
              <Link href="/dashboard/giveaway">
                <button className="w-full bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] hover:from-[#FF8B3D] hover:to-[#FF6B2C] text-white px-6 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg shadow-[#FF6B2C]/20 hover:shadow-[#FF6B2C]/40 flex items-center justify-center gap-2 group/btn">
                  Explore Giveaways
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </button>
              </Link>
            </div>
          </motion.div>

          {/* Referral Card */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            whileHover={{ y: -8 }}
            className="group relative"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#4A90E2] to-[#6BA3E8] rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-500"></div>
            <div className="relative bg-gradient-to-br from-[#1A1F2E] to-[#2A2F3E] rounded-3xl p-8 border border-[#3A3F4E] shadow-2xl hover:border-[#4A90E2] transition-all duration-500">
              {/* Icon Section */}
              <div className="flex items-center justify-between mb-6">
                <div className="bg-gradient-to-r from-[#4A90E2] to-[#6BA3E8] p-4 rounded-2xl">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div className="flex items-center gap-1 bg-[#4A90E2]/10 px-3 py-1 rounded-full">
                  <Award className="w-3 h-3 text-[#4A90E2]" />
                  <span className="text-[#4A90E2] text-xs font-semibold">PREMIUM</span>
                </div>
              </div>

              {/* Content */}
              <h3 className="text-2xl md:text-3xl font-bold mb-4 text-white">Referral Program</h3>
              <p className="text-[#8B9BB8] text-sm sm:text-base leading-relaxed mb-6">
                Build your network and earn substantial rewards by referring traders to our platform. Multi-tier commission structure with lifetime earnings potential and exclusive bonuses.
              </p>

              {/* Features List */}
              <div className="space-y-3 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#4A90E2] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Multi-Tier Commission Structure</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#4A90E2] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Lifetime Earnings Potential</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-[#4A90E2] rounded-full"></div>
                  <span className="text-[#8B9BB8] text-sm">Exclusive Referral Bonuses</span>
                </div>
              </div>

              {/* CTA Button */}
              <Link href="/dashboard/referal">
                <button className="w-full bg-gradient-to-r from-[#4A90E2] to-[#6BA3E8] hover:from-[#6BA3E8] hover:to-[#4A90E2] text-white px-6 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg shadow-[#4A90E2]/20 hover:shadow-[#4A90E2]/40 flex items-center justify-center gap-2 group/btn">
                  Refer & Earn
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </button>
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-[#FF6B2C] mb-2">$500K+</div>
            <div className="text-[#8B9BB8] text-xs sm:text-sm">Total Giveaway Rewards</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-[#4A90E2] mb-2">10K+</div>
            <div className="text-[#8B9BB8] text-xs sm:text-sm">Active Referrers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-white mb-2">95%</div>
            <div className="text-[#8B9BB8] text-xs sm:text-sm">Success Rate</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 