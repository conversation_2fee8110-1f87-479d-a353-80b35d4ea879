"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { getApiUrl } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

export default function Admin2FAPage() {
  const router = useRouter()
  const [code, setCode] = useState("")
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)
    const token = secureStorageAPI.getItem("two_factor_token")
    if (!token) {
      setError("No 2FA token found. Please login again.")
      setLoading(false)
      return
    }
    try {
      const res = await fetch(getApiUrl('verify-2fa'), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token, two_factor_code: code })
      })
      const data = await res.json()
      if (data.access_token) {
        secureStorageAPI.setItem("admin-token", data.access_token)
        secureStorageAPI.removeItem("two_factor_token")
        router.push("/adminportol")
      } else {
        setError(data.message || "2FA verification failed")
      }
    } catch (err) {
      setError("2FA verification failed. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#0a1929]">
      <form onSubmit={handleSubmit} className="bg-[#0d2339]/90 p-8 rounded-xl border border-gray-800/50 w-full max-w-md space-y-6">
        <h1 className="text-2xl font-bold text-center text-orange-400">2FA Verification</h1>
        <input
          type="text"
          placeholder="Enter 2FA code"
          value={code}
          onChange={e => setCode(e.target.value)}
          required
          className="w-full p-2 rounded bg-white/10 text-white border border-gray-700"
        />
        {error && <div className="text-red-400 text-center">{error}</div>}
        <button type="submit" disabled={loading} className="w-full bg-orange-500 hover:bg-orange-600 text-white p-2 rounded">
          {loading ? "Verifying..." : "Verify"}
        </button>
      </form>
    </div>
  )
} 