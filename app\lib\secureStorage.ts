import { AuthSecurity } from './security';

// Secure storage interface
interface SecureStorage {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

// In-memory storage for sensitive data (cleared on page refresh)
class MemoryStorage implements SecureStorage {
  private storage = new Map<string, string>();

  getItem(key: string): string | null {
    return this.storage.get(key) || null;
  }

  setItem(key: string, value: string): void {
    this.storage.set(key, value);
  }

  removeItem(key: string): void {
    this.storage.delete(key);
  }

  clear(): void {
    this.storage.clear();
  }
}

// Encrypted localStorage wrapper
class EncryptedStorage implements SecureStorage {
  private encryptionKey: string;

  constructor() {
    // Generate a unique encryption key for this session
    this.encryptionKey = AuthSecurity.generateSecureToken();
  }

  private encrypt(text: string): string {
    try {
      // Simple XOR encryption (in production, use a proper encryption library)
      let encrypted = '';
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
        encrypted += String.fromCharCode(charCode);
      }
      return btoa(encrypted); // Base64 encode
    } catch (error) {
      console.error('Encryption failed:', error);
      return '';
    }
  }

  private decrypt(encryptedText: string): string {
    try {
      const decoded = atob(encryptedText); // Base64 decode
      let decrypted = '';
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
        decrypted += String.fromCharCode(charCode);
      }
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return '';
    }
  }

  getItem(key: string): string | null {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      return this.decrypt(encrypted);
    } catch (error) {
      console.error('Failed to get encrypted item:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      const encrypted = this.encrypt(value);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to set encrypted item:', error);
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove encrypted item:', error);
    }
  }

  clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Failed to clear encrypted storage:', error);
    }
  }
}

// Secure storage manager
class SecureStorageManager {
  private memoryStorage: MemoryStorage;
  private encryptedStorage: EncryptedStorage;
  private sensitiveKeys: Set<string>;

  constructor() {
    this.memoryStorage = new MemoryStorage();
    this.encryptedStorage = new EncryptedStorage();
    this.sensitiveKeys = new Set([
      'access_token',
      'admin-token',
      'two_factor_token',
      'session-token',
      'password',
      'platform_password',
      'hashed_password'
    ]);
  }

  // Store sensitive data in memory (cleared on page refresh)
  setSensitiveItem(key: string, value: string): void {
    this.memoryStorage.setItem(key, value);
  }

  // Get sensitive data from memory
  getSensitiveItem(key: string): string | null {
    return this.memoryStorage.getItem(key);
  }

  // Store non-sensitive data in encrypted localStorage
  setItem(key: string, value: string): void {
    if (this.sensitiveKeys.has(key)) {
      this.setSensitiveItem(key, value);
    } else {
      this.encryptedStorage.setItem(key, value);
    }
  }

  // Get data from appropriate storage
  getItem(key: string): string | null {
    if (this.sensitiveKeys.has(key)) {
      return this.getSensitiveItem(key);
    } else {
      return this.encryptedStorage.getItem(key);
    }
  }

  // Remove item from appropriate storage
  removeItem(key: string): void {
    if (this.sensitiveKeys.has(key)) {
      this.memoryStorage.removeItem(key);
    } else {
      this.encryptedStorage.removeItem(key);
    }
  }

  // Clear all storage
  clear(): void {
    this.memoryStorage.clear();
    this.encryptedStorage.clear();
  }

  // Check if item exists
  hasItem(key: string): boolean {
    return this.getItem(key) !== null;
  }

  // Get all keys (for debugging only)
  getAllKeys(): string[] {
    const keys: string[] = [];
    
    // Get memory storage keys
    // Note: MemoryStorage doesn't expose keys, so we can't enumerate them
    
    // Get localStorage keys (encrypted)
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) keys.push(key);
      }
    } catch (error) {
      console.error('Failed to get storage keys:', error);
    }
    
    return keys;
  }

  // Secure logout - clear all sensitive data
  secureLogout(): void {
    this.memoryStorage.clear();
    // Clear specific sensitive keys from localStorage
    this.sensitiveKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Failed to remove ${key}:`, error);
      }
    });
  }
}

// Create singleton instance
const secureStorage = new SecureStorageManager();

// Export secure storage functions
export const secureStorageAPI = {
  // Sensitive data storage (memory only)
  setSensitiveItem: (key: string, value: string) => secureStorage.setSensitiveItem(key, value),
  getSensitiveItem: (key: string) => secureStorage.getSensitiveItem(key),
  
  // General storage (encrypted)
  setItem: (key: string, value: string) => secureStorage.setItem(key, value),
  getItem: (key: string) => secureStorage.getItem(key),
  removeItem: (key: string) => secureStorage.removeItem(key),
  clear: () => secureStorage.clear(),
  hasItem: (key: string) => secureStorage.hasItem(key),
  
  // Security functions
  secureLogout: () => secureStorage.secureLogout(),
  getAllKeys: () => secureStorage.getAllKeys(),
};

// Migration helper to move existing localStorage data to secure storage
export const migrateToSecureStorage = () => {
  try {
    const keysToMigrate = [
      'access_token',
      'admin-token',
      'two_factor_token',
      'session-token',
      'verification_email',
      'reset_email',
      'selectedAccountId',
      'Name',
      'accountStatus',
      'platform_login',
      'server',
      'password',
      'session_id',
      'terminal_id',
      'profit_target',
      'orderFailed'
    ];

    keysToMigrate.forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        secureStorage.setItem(key, value);
        // Remove from localStorage after migration
        localStorage.removeItem(key);
      }
    });

    console.log('Migration to secure storage completed');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

// Auto-migrate on module load
if (typeof window !== 'undefined') {
  migrateToSecureStorage();
}

export default secureStorageAPI; 