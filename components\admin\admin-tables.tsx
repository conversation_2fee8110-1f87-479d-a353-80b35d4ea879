import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Refresh<PERSON><PERSON>, Eye, Edit, Receipt } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { EditOrderModal } from "@/components/edit-order-modal"
import { ViewOrderModal } from "@/components/view-order-modal"
import { FailOrderModal } from "@/components/fail-order-modal"
import { PassOrderModal } from "@/components/pass-order-modal"
import { PaymentProofModal } from "@/components/payment-proof-modal"
import { toast } from "@/components/ui/use-toast"
import { CertificateTable } from "@/components/certificate-table"
import { OrderStatus, OrderType, OrderDetails } from "@/types/order"

interface AdminTablesProps {
  selectedSection: "users" | "orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "certificates" | null;
}

interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  country: string;
  phone_no: string;
  address: string;
  hashed_password: string;
  createdAt: string;
}

interface RunningOrder {
  order_id: number;
  platform_login: string;
  platform_password: string;
  server: string;
  session_id: string;
  terminal_id: string;
}

export function AdminTables({ selectedSection }: AdminTablesProps) {
  const [users, setUsers] = useState<User[]>([])
  const [runningOrders, setRunningOrders] = useState<RunningOrder[]>([])
  const [completedOrders, setCompletedOrders] = useState<OrderDetails[]>([])
  const [failedOrders, setFailedOrders] = useState<OrderDetails[]>([])
  const [passOrders, setPassOrders] = useState<OrderDetails[]>([])
  const [stageTwoOrders, setStageTwoOrders] = useState<OrderDetails[]>([])
  const [liveOrders, setLiveOrders] = useState<OrderDetails[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [isMobile, setIsMobile] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showPaymentProof, setShowPaymentProof] = useState<OrderDetails | null>(null)

  useEffect(() => {
    setMounted(true)
    const checkMobile = () => setIsMobile(window.innerWidth <= 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/proxy/auth/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(Array.isArray(data) ? data : [])
      } else {
        setUsers([])
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      setUsers([])
    }
  }

  const fetchRunningOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/running_orders')
      if (response.ok) {
        const data = await response.json()
        setRunningOrders(Array.isArray(data) ? data : [])
      } else {
        setRunningOrders([])
      }
    } catch (error) {
      console.error('Error fetching running orders:', error)
      setRunningOrders([])
    }
  }

  const fetchCompletedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/completed_orders')
      if (response.ok) {
        const data = await response.json()
        console.log('Completed orders raw data:', data)
        const mappedOrders = data.map((order: any) => {
          console.log('Processing order:', order)
          return {
            id: order.complete_order_id || order.id,
            user: {
              name: order.username || 'N/A',
              email: order.email || 'N/A',
            },
            amount: order.account_size || '0',
            status: 'Completed',
            createdAt: order.date || new Date().toISOString(),
            accountType: order.challenge_type || 'Standard',
            platformType: order.platform || 'MT4',
            paymentMethod: order.payment_method || 'N/A',
            txid: order.txid || 'N/A',
            paymentProof: order.payment_proof || order.paymentProof || order.image || order.image_url || null,
            image: order.image || order.image_url || order.payment_proof || order.paymentProof || null,
          }
        })
        console.log('Mapped completed orders:', mappedOrders)
        setCompletedOrders(mappedOrders)
      } else {
        setCompletedOrders([])
      }
    } catch (error) {
      console.error('Error fetching completed orders:', error)
      setCompletedOrders([])
    }
  }

  const fetchFailedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/failed_orders')
      if (response.ok) {
        const data = await response.json()
        console.log('Failed orders raw data:', data)
        const mappedOrders = data.map((order: any) => {
          console.log('Processing failed order:', order)
          return {
            id: order.fail_order_id || order.id,
            user: {
              name: order.username || 'N/A',
              email: order.email || 'N/A',
            },
            amount: order.account_size || '0',
            status: 'Failed',
            createdAt: order.date || new Date().toISOString(),
            accountType: order.challenge_type || 'Standard',
            platformType: order.platform || 'MT4',
            paymentMethod: order.payment_method || 'N/A',
            txid: order.txid || 'N/A',
            paymentProof: order.payment_proof || order.paymentProof || order.image || order.image_url || null,
            image: order.image || order.image_url || order.payment_proof || order.paymentProof || null,
          }
        })
        console.log('Mapped failed orders:', mappedOrders)
        setFailedOrders(mappedOrders)
      } else {
        setFailedOrders([])
      }
    } catch (error) {
      console.error('Error fetching failed orders:', error)
      setFailedOrders([])
    }
  }

  const fetchPassedOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/passed_orders')
      if (response.ok) {
        const data = await response.json()
        console.log('Passed orders raw data:', data)
        const mappedOrders = data.map((order: any) => {
          console.log('Processing passed order:', order)
          return {
            id: order.pass_order_id || order.id,
            user: {
              name: order.username || 'N/A',
              email: order.email || 'N/A',
            },
            amount: order.account_size || '0',
            status: 'Passed',
            createdAt: order.date || new Date().toISOString(),
            accountType: order.challenge_type || 'Standard',
            platformType: order.platform || 'MT4',
            paymentMethod: order.payment_method || 'N/A',
            txid: order.txid || 'N/A',
            paymentProof: order.payment_proof || order.paymentProof || order.image || order.image_url || null,
            image: order.image || order.image_url || order.payment_proof || order.paymentProof || null,
          }
        })
        console.log('Mapped passed orders:', mappedOrders)
        setPassOrders(mappedOrders)
      } else {
        setPassOrders([])
      }
    } catch (error) {
      console.error('Error fetching passed orders:', error)
      setPassOrders([])
    }
  }

  const fetchStageTwoOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/stage_two_orders')
      if (response.ok) {
        const data = await response.json()
        console.log('Stage two orders raw data:', data)
        const mappedOrders = data.map((order: any) => {
          console.log('Processing stage two order:', order)
          return {
            id: order.stage_two_order_id || order.id,
            user: {
              name: order.username || 'N/A',
              email: order.email || 'N/A',
            },
            amount: order.account_size || '0',
            status: 'Stage Two',
            createdAt: order.date || new Date().toISOString(),
            accountType: order.challenge_type || 'Standard',
            platformType: order.platform || 'MT4',
            paymentMethod: order.payment_method || 'N/A',
            txid: order.txid || 'N/A',
            paymentProof: order.payment_proof || order.paymentProof || order.image || order.image_url || null,
            image: order.image || order.image_url || order.payment_proof || order.paymentProof || null,
          }
        })
        console.log('Mapped stage two orders:', mappedOrders)
        setStageTwoOrders(mappedOrders)
      } else {
        setStageTwoOrders([])
      }
    } catch (error) {
      console.error('Error fetching stage two orders:', error)
      setStageTwoOrders([])
    }
  }

  const fetchLiveOrders = async () => {
    try {
      const response = await fetch('/api/proxy/order/live_orders')
      if (response.ok) {
        const data = await response.json()
        console.log('Live orders raw data:', data)
        const mappedOrders = data.map((order: any) => {
          console.log('Processing live order:', order)
          return {
            id: order.live_order_id || order.id,
            user: {
              name: order.username || 'N/A',
              email: order.email || 'N/A',
            },
            amount: order.account_size || '0',
            status: 'Live',
            createdAt: order.date || new Date().toISOString(),
            accountType: order.challenge_type || 'Standard',
            platformType: order.platform || 'MT4',
            paymentMethod: order.payment_method || 'N/A',
            txid: order.txid || 'N/A',
            paymentProof: order.payment_proof || order.paymentProof || order.image || order.image_url || null,
            image: order.image || order.image_url || order.payment_proof || order.paymentProof || null,
          }
        })
        console.log('Mapped live orders:', mappedOrders)
        setLiveOrders(mappedOrders)
      } else {
        setLiveOrders([])
      }
    } catch (error) {
      console.error('Error fetching live orders:', error)
      setLiveOrders([])
    }
  }

  useEffect(() => {
    if (!selectedSection) return
    
    switch (selectedSection) {
      case "users":
        fetchUsers()
        break
      case "runningOrders":
        fetchRunningOrders()
        break
      case "completedOrders":
        fetchCompletedOrders()
        break
      case "failedOrders":
        fetchFailedOrders()
        break
      case "passOrders":
        fetchPassedOrders()
        break
      case "stageTwoOrders":
        fetchStageTwoOrders()
        break
      case "liveOrders":
        fetchLiveOrders()
        break
    }
  }, [selectedSection])

  const refreshData = async () => {
    setIsRefreshing(true)
    try {
      switch (selectedSection) {
        case "users":
          await fetchUsers()
          break
        case "runningOrders":
          await fetchRunningOrders()
          break
        case "completedOrders":
          await fetchCompletedOrders()
          break
        case "failedOrders":
          await fetchFailedOrders()
          break
        case "passOrders":
          await fetchPassedOrders()
          break
        case "stageTwoOrders":
          await fetchStageTwoOrders()
          break
        case "liveOrders":
          await fetchLiveOrders()
          break
      }
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }

  const handlePassOrder = async (orderId: number) => {
    try {
      const response = await fetch(`/api/proxy/order/pass_order/${orderId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      if (response.ok) {
        toast({ title: "Success", description: "Order passed successfully" })
        refreshData()
      }
    } catch (error) {
      toast({ title: "Error", description: "Failed to pass order", variant: "destructive" })
    }
  }

  const handleFailOrder = async (orderId: number, reason: string) => {
    try {
      const response = await fetch(`/api/proxy/order/fail_order/${orderId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason }),
      })
      if (response.ok) {
        toast({ title: "Success", description: "Order failed successfully" })
        refreshData()
      }
    } catch (error) {
      toast({ title: "Error", description: "Failed to fail order", variant: "destructive" })
    }
  }

  const handleConfirmOrder = async (order: OrderDetails) => {
    try {
      const response = await fetch(`/api/proxy/order/confirm_order/${order.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })
      if (response.ok) {
        toast({ title: "Success", description: "Order confirmed successfully" })
        refreshData()
      }
    } catch (error) {
      toast({ title: "Error", description: "Failed to confirm order", variant: "destructive" })
    }
  }

  const handleSaveChanges = async (order: OrderDetails) => {
    try {
      const response = await fetch(`/api/proxy/order/update_order/${order.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(order),
      })
      if (response.ok) {
        toast({ title: "Success", description: "Order updated successfully" })
        refreshData()
      }
    } catch (error) {
      toast({ title: "Error", description: "Failed to update order", variant: "destructive" })
    }
  }

  const handleRejectOrder = async (order: OrderDetails, reason: string) => {
    try {
      const response = await fetch(`/api/proxy/order/reject_order/${order.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason }),
      })
      if (response.ok) {
        toast({ title: "Success", description: "Order rejected successfully" })
        refreshData()
      }
    } catch (error) {
      toast({ title: "Error", description: "Failed to reject order", variant: "destructive" })
    }
  }

  const handleViewOrder = (order: OrderDetails) => {
    console.log('Viewing order:', order)
  }

  const getFilteredData = () => {
    let data: any[] = []
    
    switch (selectedSection) {
      case "users":
        data = users
        break
      case "runningOrders":
        data = runningOrders
        break
      case "completedOrders":
        data = completedOrders
        break
      case "failedOrders":
        data = failedOrders
        break
      case "passOrders":
        data = passOrders
        break
      case "stageTwoOrders":
        data = stageTwoOrders
        break
      case "liveOrders":
        data = liveOrders
        break
    }

    if (!searchTerm) return data

    return data.filter((item: any) => {
      const searchLower = searchTerm.toLowerCase()
      
      if (selectedSection === "users") {
        return (
          item.username?.toLowerCase().includes(searchLower) ||
          item.email?.toLowerCase().includes(searchLower) ||
          item.name?.toLowerCase().includes(searchLower)
        )
      } else {
        return (
          item.user?.name?.toLowerCase().includes(searchLower) ||
          item.user?.email?.toLowerCase().includes(searchLower) ||
          item.amount?.toString().toLowerCase().includes(searchLower) ||
          item.status?.toLowerCase().includes(searchLower) ||
          item.accountType?.toLowerCase().includes(searchLower) ||
          item.platformType?.toLowerCase().includes(searchLower) ||
          item.paymentMethod?.toLowerCase().includes(searchLower) ||
          item.txid?.toLowerCase().includes(searchLower) ||
          item.id?.toString().includes(searchLower)
        )
      }
    })
  }

  const paginatedData = getFilteredData().slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(getFilteredData().length / itemsPerPage)

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  const renderPaginationControls = () => {
    if (totalPages <= 1) return null

    return (
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-400">
          Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, getFilteredData().length)} of {getFilteredData().length} results
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-400">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    )
  }

  const renderTxid = (txid: string | undefined) => {
    if (!txid) return "-"
    return (
      <div
        className="cursor-pointer"
        onClick={() => {
          navigator.clipboard.writeText(txid)
          toast({ title: "Success", description: "Transaction ID copied to clipboard" })
        }}
      >
        {txid}
      </div>
    )
  }

  const getDisplayStatus = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="default">{status}</Badge>
      case "ACTIVE":
      case "COMPLETED":
      case "Passed":
      case "Live":
        return <Badge variant="secondary" className="bg-green-500">{status}</Badge>
      case "FAILED":
      case "Failed":
        return <Badge variant="destructive">{status}</Badge>
      case "Stage Two":
        return <Badge variant="secondary" className="bg-blue-500">{status}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const renderTable = () => {
    switch (selectedSection) {
      case "users":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4 flex items-center justify-between">
              <Input
                placeholder="Search by username, email, or name..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isRefreshing}
                className="bg-blue-500 text-white hover:bg-blue-600"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">ID</TableHead>
                  <TableHead className="text-xs">Username</TableHead>
                  <TableHead className="text-xs">Email</TableHead>
                  <TableHead className="text-xs">Name</TableHead>
                  <TableHead className="text-xs">Country</TableHead>
                  <TableHead className="text-xs">Phone</TableHead>
                  {!isMobile && <TableHead className="text-xs">Address</TableHead>}
                  <TableHead className="text-xs">Created At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((user: User) => (
                  <TableRow key={user.id}>
                    <TableCell className="text-xs">{user.id}</TableCell>
                    <TableCell className="text-xs">{user.username}</TableCell>
                    <TableCell className="text-xs">{user.email}</TableCell>
                    <TableCell className="text-xs">{user.name}</TableCell>
                    <TableCell className="text-xs">{user.country}</TableCell>
                    <TableCell className="text-xs">{user.phone_no}</TableCell>
                    {!isMobile && <TableCell className="text-xs">{user.address}</TableCell>}
                    <TableCell className="text-xs">{user.createdAt}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
          </div>
        )

      case "runningOrders":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4 flex items-center justify-between">
              <Input
                placeholder="Search by order ID, platform login, or server..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isRefreshing}
                className="bg-blue-500 text-white hover:bg-blue-600"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Platform Login</TableHead>
                  <TableHead className="text-xs">Platform Password</TableHead>
                  <TableHead className="text-xs">Server</TableHead>
                  <TableHead className="text-xs">Session ID</TableHead>
                  <TableHead className="text-xs">Terminal ID</TableHead>
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((order: RunningOrder) => (
                  <TableRow key={order.order_id}>
                    <TableCell className="text-xs">{order.order_id}</TableCell>
                    <TableCell className="text-xs">{order.platform_login}</TableCell>
                    <TableCell className="text-xs">{order.platform_password}</TableCell>
                    <TableCell className="text-xs">{order.server}</TableCell>
                    <TableCell className="text-xs">{order.session_id}</TableCell>
                    <TableCell className="text-xs">{order.terminal_id}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <PassOrderModal 
                          order={{
                            order_id: order.order_id,
                            ...order
                          }}
                          onPass={handlePassOrder}
                        />
                        <FailOrderModal
                          orderId={order.order_id}
                          onFail={handleFailOrder}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
          </div>
        )

      case "completedOrders":
      case "failedOrders":
      case "passOrders":
      case "stageTwoOrders":
      case "liveOrders":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4 flex items-center justify-between">
              <Input
                placeholder="Search by name, email, status, account type, platform, payment method, or order ID..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isRefreshing}
                className="bg-blue-500 text-white hover:bg-blue-600"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Name</TableHead>
                  <TableHead className="text-xs">Account Type</TableHead>
                  <TableHead className="text-xs">Amount</TableHead>
                  <TableHead className="text-xs">Platform</TableHead>
                  <TableHead className="text-xs">Transaction ID</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  {!isMobile && <TableHead className="text-xs">Created At</TableHead>}
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((order: OrderDetails) => (
                  <TableRow key={order.id}>
                    <TableCell className="text-xs font-medium">{order.id}</TableCell>
                    <TableCell className="text-xs">{order.user?.name || 'N/A'}</TableCell>
                    <TableCell className="text-xs">{order.accountType || 'N/A'}</TableCell>
                    <TableCell className="text-xs">${order.amount || 0}</TableCell>
                    <TableCell className="text-xs">{order.platformType || 'N/A'}</TableCell>
                    <TableCell className="text-xs">
                      {renderTxid(order.txid)}
                    </TableCell>
                    <TableCell className="text-xs">{getDisplayStatus(order.status || "")}</TableCell>
                    {!isMobile && <TableCell className="text-xs">{order.createdAt || 'N/A'}</TableCell>}
                    <TableCell>
                      <div className={`flex ${isMobile ? 'flex-col' : 'flex-wrap'} gap-2`}>
                        <EditOrderModal
                          order={order}
                          onSave={handleSaveChanges}
                          onReject={handleRejectOrder}
                          onFail={handleFailOrder}
                        />
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleConfirmOrder(order)}
                          className="w-full md:w-auto text-xs bg-blue-500 text-white hover:bg-blue-600"
                        >
                          Confirm
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            console.log('Order data:', order)
                            console.log('Payment proof:', order.paymentProof)
                            console.log('Image:', order.image)
                            setShowPaymentProof(order)
                          }}
                          className="w-full md:w-auto bg-purple-500 text-white hover:bg-purple-600 text-xs"
                        >
                          Payment Proof
                        </Button>
                        <ViewOrderModal
                          order={order}
                          onView={handleViewOrder}
                          status={order.status}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
            <PaymentProofModal
              order={showPaymentProof}
              onClose={() => setShowPaymentProof(null)}
            />
          </div>
        )

      case "certificates":
        return <CertificateTable />

      default:
        return null
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-xs">
            {selectedSection ? selectedSection.charAt(0).toUpperCase() + selectedSection.slice(1) : "Select a section"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderTable()}
        </CardContent>
      </Card>
    </motion.div>
  )
} 