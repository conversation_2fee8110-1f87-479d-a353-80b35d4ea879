"use client";
import { useState, ChangeEvent, FormEvent, useEffect, Suspense } from "react";
import { getApiUrl, getEndpointPath } from "../config/api";
import secureStorageAPI from '@/app/lib/secureStorage';
import SecureInput from '@/app/components/SecureInput';

// This tells Next.js that this page should be dynamically rendered
export const dynamic = 'force-dynamic';
export const dynamicParams = true;
import { Navbar } from "../component/navbar";
import Link from "next/link";
import { motion } from "framer-motion";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useRouter, useSearchParams } from 'next/navigation';
import { Bitcoin, DollarSign, Eye, EyeOff } from 'lucide-react';

interface ResetPasswordData {
  password: string;
  confirmPassword: string;
}

interface InputFieldProps {
  label: string;
  type: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  name: string;
  className?: string;
  showPassword?: boolean;
  toggleShowPassword?: () => void;
}

function InputField({
  label,
  type,
  placeholder,
  value,
  onChange,
  name,
  className = "",
  showPassword,
  toggleShowPassword,
}: InputFieldProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col gap-2"
    >
      <label className="text-sm font-medium text-gray-300">{label}</label>
      <div className="relative">
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          name={name}
          className={`w-full px-4 py-3 rounded-lg bg-black/50 text-white border border-gray-700 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300 ${className}`}
        />
        {toggleShowPassword && (
          <button
            type="button"
            onClick={toggleShowPassword}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-orange-400 focus:outline-none"
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
    </motion.div>
  );
}

const FloatingIcons = () => {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1000,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            x: Math.random() * dimensions.width,
            y: Math.random() * dimensions.height,
            scale: 0.5 + Math.random() * 0.5,
            opacity: 0.3 + Math.random() * 0.4
          }}
          animate={{
            x: [null, Math.random() * dimensions.width],
            y: [null, Math.random() * dimensions.height],
            rotate: [0, 360]
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute text-gray-600"
        >
          {i % 3 === 0 ? (
            <Bitcoin size={24} />
          ) : (
            <DollarSign size={24} />
          )}
        </motion.div>
      ))}
    </div>
  );
};

// Create a component for when no token is provided
function NoTokenFallback() {
  const router = useRouter();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-gray-900 to-black rounded-2xl shadow-2xl p-6 md:p-8 lg:p-10 w-full max-w-md mt-20 mx-auto border border-gray-800 relative z-10"
    >
      <motion.h1
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 mb-8 text-center"
      >
        Password Reset Error
      </motion.h1>

      <div className="bg-red-900/30 border border-red-500/50 rounded-lg p-4 mb-6">
        <p className="text-red-400 font-medium">Invalid or Missing Reset Token</p>
        <p className="text-gray-400 mt-2">The password reset link appears to be invalid or expired.</p>
      </div>

      <p className="text-gray-400 text-center mb-6">
        Please request a new password reset link to continue.
      </p>

      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <button
          onClick={() => router.push('/forgot-password')}
          className="w-full bg-gradient-to-r from-orange-400 to-orange-500 text-white font-bold py-4 rounded-lg hover:from-orange-500 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/25"
        >
          Request New Reset Link
        </button>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="mt-6 text-center space-y-3"
      >
        <p className="text-gray-400">
          Remember your password?{" "}
          <Link href="/sigin" className="text-orange-400 hover:text-orange-500 hover:underline transition duration-300">
            Sign In
          </Link>
        </p>
      </motion.div>
    </motion.div>
  );
}

// Create a component for the form content
function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [resetPasswordData, setResetPasswordData] = useState<ResetPasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [token, setToken] = useState("");
  const [loading, setLoading] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [hasToken, setHasToken] = useState(false);

  useEffect(() => {
    // Only run on client-side
    if (typeof window !== 'undefined') {
      // Get token from URL query parameter
      const tokenParam = searchParams?.get('token');

      // Also check if token is in the URL path (for compatibility)
      const urlPath = window.location.href;
      const tokenInPath = urlPath.includes('token=') ?
        urlPath.split('token=')[1].split('&')[0] : null;

      const finalToken = tokenParam || tokenInPath;

      if (finalToken) {
        setToken(finalToken);
        setHasToken(true);
      }
    }
  }, [searchParams]);

  // If no token is present, show the NoTokenFallback
  if (!hasToken) {
    return <NoTokenFallback />;
  }

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setResetPasswordData({ ...resetPasswordData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate passwords match
    if (resetPasswordData.password !== resetPasswordData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setLoading(true);

    try {
      // Construct URL with query parameters
      const url = getApiUrl(`auth/reset-password?token=${encodeURIComponent(token)}&new_password=${encodeURIComponent(resetPasswordData.password)}`);
      const response = await fetch(url, {
        method: "POST",
      });

      if (response.ok) {
        toast.success('Password has been reset successfully!');
        // Clear stored email
        secureStorageAPI.removeItem('reset_email');
        // Redirect to login page after successful reset
        setTimeout(() => {
          router.push('/sigin');
        }, 2000);
      } else {
        console.error("Password reset failed");
        toast.error('Failed to reset password. Please try again.');
      }
    } catch (error) {
      console.error("Password reset failed:", error);
      toast.error('Network error. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-gray-900 to-black rounded-2xl shadow-2xl p-6 md:p-8 lg:p-10 w-full max-w-md mt-20 mx-auto border border-gray-800 relative z-10"
    >
      <motion.h1
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 mb-8 text-center"
      >
        Reset Password
      </motion.h1>

      {userEmail && (
        <p className="text-gray-400 text-center mb-6">
          Create a new password for <span className="text-orange-400">{userEmail}</span>
        </p>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">

        <SecureInput
          label="New Password"
          type={showPassword ? "text" : "password"}
          placeholder="Enter your new password"
          value={resetPasswordData.password}
          onChange={handleChange}
          name="password"
          validation={{
            required: true,
            minLength: 8,
            customValidation: (value) => {
              const hasUpperCase = /[A-Z]/.test(value);
              const hasLowerCase = /[a-z]/.test(value);
              const hasNumbers = /\d/.test(value);
              const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
              
              if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
                return { 
                  isValid: false, 
                  error: 'Password must contain uppercase, lowercase, number, and special character' 
                };
              }
              return { isValid: true };
            }
          }}
        />

        <SecureInput
          label="Confirm Password"
          type={showConfirmPassword ? "text" : "password"}
          placeholder="Confirm your new password"
          value={resetPasswordData.confirmPassword}
          onChange={handleChange}
          name="confirmPassword"
          validation={{
            required: true,
            customValidation: (value) => {
              if (value !== resetPasswordData.password) {
                return { isValid: false, error: 'Passwords do not match' };
              }
              return { isValid: true };
            }
          }}
        />

        <motion.div
          whileHover={{ scale: loading ? 1 : 1.02 }}
          whileTap={{ scale: loading ? 1 : 0.98 }}
        >
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-orange-400 to-orange-500 text-white font-bold py-4 rounded-lg hover:from-orange-500 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/25 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : (
              'Reset Password'
            )}
          </button>
        </motion.div>
      </form>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="mt-6 text-center space-y-3"
      >
        <p className="text-gray-400">
          Remember your password?{" "}
          <Link href="/sigin" className="text-orange-400 hover:text-orange-500 hover:underline transition duration-300">
            Sign In
          </Link>
        </p>
      </motion.div>
    </motion.div>
  );
}

// Main component with Suspense boundary
export default function ResetPassword() {
  return (
    <div className="min-h-screen bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-900 via-black to-black">
      <Navbar />
        <FloatingIcons />
      <div className="container mx-auto px-4 py-8">
        <Suspense fallback={
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
          </div>
        }>
          <ResetPasswordForm />
        </Suspense>
      </div>
      <ToastContainer position="bottom-right" />
    </div>
  );
}


