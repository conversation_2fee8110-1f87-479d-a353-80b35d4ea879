import { headers } from 'next/headers';
import { getApiUrl, getEndpointPath } from '@/app/config/api';
import { createSecureResponse } from '@/app/lib/api-helpers';

export async function POST(request: Request) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return createSecureResponse({
        message: 'Unauthorized',
        status: 401
      });
    }

    const formData = await request.formData();
    const session = formData.get('session');
    const accountId = formData.get('account_id');

    console.log('MyFXBook fetch request:', { session, accountId });

    if (!session || !accountId) {
      return createSecureResponse({
        message: 'Session and account_id are required',
        status: 400
      });
    }

    const response = await fetch(getApiUrl('myfxbook/fetch_account_details'), {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error(`Backend request failed with status: ${response.status}`);
      return createSecureResponse({
        message: `Failed to fetch MyFXBook account details (Status: ${response.status})`,
        status: response.status
      });
    }

    const data = await response.json();
    console.log('MyFXBook response:', data);

    return createSecureResponse({
      data,
      status: 200
    });
  } catch (error) {
    console.error('Error in MyFXBook fetch API:', error);
    return createSecureResponse({
      message: 'Internal server error',
      status: 500
    });
  }
}
