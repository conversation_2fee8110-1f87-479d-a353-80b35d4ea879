import { NextRequest, NextResponse } from 'next/server';

// Simple security utilities
export const SECURITY_CONFIG = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000,
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000,
  PASSWORD_MIN_LENGTH: 8,
  RATE_LIMIT_WINDOW: 60 * 1000,
  RATE_LIMIT_MAX_REQUESTS: 100,
};

// Input validation
export const validateInput = (input: any, type?: string) => {
  if (!input) return false;
  
  switch (type) {
    case 'email':
      return typeof input === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    case 'password':
      return typeof input === 'string' && input.length >= SECURITY_CONFIG.PASSWORD_MIN_LENGTH;
    default:
      return typeof input === 'string' && input.trim().length > 0;
  }
};

// Input sanitization
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .trim();
};

// CSRF token generation
export const generateCSRFToken = (): string => {
  if (typeof globalThis !== 'undefined' && globalThis.crypto && globalThis.crypto.getRandomValues) {
    const array = new Uint8Array(32);
    globalThis.crypto.getRandomValues(array);
    return Array.from(array).map(b => b.toString(16).padStart(2, '0')).join('');
  } else {
    return Array.from({ length: 64 }, () => Math.floor(Math.random() * 16).toString(16)).join('');
  }
};

// CSRF token validation
export const validateCSRFToken = (token: string, storedToken: string): boolean => {
  if (!token || !storedToken) return false;
  return token === storedToken;
};

// Security headers
export const setSecurityHeaders = () => ({
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; '),
});

// Auth token validation
export const validateAuthToken = (token: string): boolean => {
  if (!token || typeof token !== 'string') return false;
  return token.length > 10;
};

// Rate limiting
interface RateLimitConfig {
  windowMs: number;
  max: number;
  message?: string;
}

interface RateLimitResult {
  success: boolean;
  message?: string;
  remainingAttempts?: number;
  resetTime?: number;
}

const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (config: RateLimitConfig) => {
  return async (request: NextRequest): Promise<RateLimitResult> => {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const identifier = `${ip}-${request.nextUrl.pathname}`;
    const now = Date.now();
    
    const userAttempts = rateLimitStore.get(identifier);
    
    if (!userAttempts || now > userAttempts.resetTime) {
      rateLimitStore.set(identifier, {
        count: 1,
        resetTime: now + config.windowMs,
      });
      return {
        success: true,
        remainingAttempts: config.max - 1,
        resetTime: now + config.windowMs,
      };
    }
    
    if (userAttempts.count >= config.max) {
      return {
        success: false,
        message: config.message || 'Too many requests',
        remainingAttempts: 0,
        resetTime: userAttempts.resetTime,
      };
    }
    
    userAttempts.count++;
    return {
      success: true,
      remainingAttempts: config.max - userAttempts.count,
      resetTime: userAttempts.resetTime,
    };
  };
};
