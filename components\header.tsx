"use client"

import { Setting<PERSON>, LogOut, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AccountSwitcher } from "@/components/account-switcher"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { getApiUrl, getEndpointPath } from "../app/config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

interface Account {
  id: string
  name: string
  username: string
  order_id: string
  balance: number
}

interface HeaderProps {
  isAdmin?: boolean
  onAccountSwitch?: (account: Account) => void
}

interface AccountData {
  order_id: string
  account_size: string
  platform: string
  payment_method: string
  status?: string
}

export function Header({ isAdmin, onAccountSwitch }: HeaderProps) {
  const [accounts, setAccounts] = useState<AccountData[]>([])
  const [isScrolled, setIsScrolled] = useState(false)
  const [username, setUsername] = useState("")
  const [selectedAccountId, setSelectedAccountId] = useState("")

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    const storedName = secureStorageAPI.getItem('Name')
    const storedAccountId = secureStorageAPI.getItem('selectedAccountId')
    if (storedName) {
      setUsername(storedName)
    }
    if (storedAccountId) {
      setSelectedAccountId(storedAccountId)
    }
  }, [])

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const accessToken = secureStorageAPI.getItem('access_token')
        if (!accessToken) {
          console.error('No access token found')
          return
        }

        const response = await fetch(getApiUrl('order/order_ids'), {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const accountData = await response.json()
        const validAccounts = Array.isArray(accountData) ? accountData : []
        setAccounts(validAccounts)

        if (validAccounts.length > 0) {
          const selectedAccount = validAccounts.find(
            (acc) => acc.order_id.replace(/[^\d]/g, '') === selectedAccountId
          )
          
          if (selectedAccount?.status) {
            secureStorageAPI.setItem('accountStatus', selectedAccount.status)
          }

          if (!selectedAccountId) {
            const firstAccount = validAccounts[0]
            const numericId = firstAccount.order_id.replace(/[^\d]/g, '')
            handleAccountSwitch({
              id: firstAccount.order_id,
              name: firstAccount.order_id,
              username: firstAccount.order_id,
              order_id: numericId,
              balance: 0
            })
          } else {
            const currentAccount = validAccounts.find(
              (acc) => acc.order_id.replace(/[^\d]/g, '') === selectedAccountId
            )
            if (currentAccount) {
              setUsername(currentAccount.order_id)
              secureStorageAPI.setItem('Name', currentAccount.order_id)
            }
          }
        }
      } catch (error) {
        console.error('Error fetching accounts:', error)
      }
    }

    fetchAccounts()
  }, [selectedAccountId])

  const handleAccountSwitch = (account: Account) => {
    try {
      const numericId = account.order_id.replace(/[^\d]/g, '')
      
      secureStorageAPI.setItem('selectedAccountId', numericId)
      secureStorageAPI.setItem('Name', account.username)
      
      window.dispatchEvent(new Event('storage'))
      
      setSelectedAccountId(numericId)
      setUsername(account.username)
      
      if (onAccountSwitch) {
        onAccountSwitch({
          ...account,
          order_id: numericId
        })
      }
    } catch (error) {
      console.error('Error switching accounts:', error)
    }
  }

  const formattedAccounts = accounts.map(account => ({
    id: account.order_id,
    name: account.order_id,
    username: account.order_id,
    order_id: account.order_id.replace(/[^\d]/g, ''),
    balance: 0
  }))

  return (
    <motion.header 
      className={`sticky top-0 z-40 w-full border-b border-gray-800/50 bg-[#0a1929]/95 backdrop-blur-lg transition-all duration-300 ${
        isScrolled ? 'shadow-lg shadow-blue-900/20' : ''
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 100 }}
    >
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <AccountSwitcher 
          accounts={formattedAccounts}
          onSwitch={handleAccountSwitch}
          selectedAccountId={selectedAccountId}
        />
        <div className="flex items-center gap-4">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="gap-2 group">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                    <span className="text-white font-medium">{username ? username[0].toUpperCase() : 'A'}</span>
                  </div>
                  <span className="hidden md:inline group-hover:text-blue-400">{username || 'Account'}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 backdrop-blur-lg bg-[#0a1929]/95">
                <DropdownMenuLabel className="text-blue-400">My Account</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-700" />
                <DropdownMenuItem className="hover:bg-blue-500/10 cursor-pointer gap-2">
                  <User className="h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:bg-blue-500/10 cursor-pointer gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-500 hover:bg-red-500/10 cursor-pointer gap-2">
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
        </div>
      </div>
    </motion.header>
  )
}