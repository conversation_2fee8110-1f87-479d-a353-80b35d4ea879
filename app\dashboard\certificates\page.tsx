"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Award, 
  Search, 
  Download, 
  Eye, 
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Star,
  Shield,
  FileText,
  Copy,
  ExternalLink,
  Filter,
  Plus,
  Verified,
  AlertTriangle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "react-toastify"

interface Certificate {
  id: string
  certificateId: string
  type: 'trading' | 'completion' | 'achievement' | 'verification'
  title: string
  description: string
  status: 'active' | 'expired' | 'pending' | 'revoked'
  issueDate: string
  expiryDate?: string
  issuer: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  score?: number
  verificationUrl: string
  downloadUrl: string
  metadata: {
    program: string
    duration: string
    requirements: string[]
    achievements: string[]
  }
}

interface VerificationResult {
  isValid: boolean
  certificate?: Certificate
  message: string
  verifiedAt: string
}

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([])
  const [filteredCertificates, setFilteredCertificates] = useState<Certificate[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [levelFilter, setLevelFilter] = useState<string>("all")
  const [verificationId, setVerificationId] = useState("")
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)
  const [selectedCertificate, setSelectedCertificate] = useState<Certificate | null>(null)

  useEffect(() => {
    fetchCertificates()
  }, [])

  useEffect(() => {
    filterCertificates()
  }, [certificates, searchTerm, typeFilter, statusFilter, levelFilter])

  const fetchCertificates = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockCertificates: Certificate[] = [
        {
          id: "1",
          certificateId: "FH-CERT-2024-001",
          type: "trading",
          title: "Professional Trading Certification",
          description: "Advanced trading strategies and risk management certification",
          status: "active",
          issueDate: "2024-01-15",
          expiryDate: "2025-01-15",
          issuer: "Funded Horizon",
          level: "advanced",
          score: 95,
          verificationUrl: "https://verify.fundedhorizon.com/cert/FH-CERT-2024-001",
          downloadUrl: "/api/certificates/FH-CERT-2024-001/download",
          metadata: {
            program: "Advanced Trading Program",
            duration: "6 months",
            requirements: ["Complete 100 trades", "Maintain 70% win rate", "Pass final assessment"],
            achievements: ["Risk Management Excellence", "Technical Analysis Mastery", "Portfolio Optimization"]
          }
        },
        {
          id: "2",
          certificateId: "FH-CERT-2024-002",
          type: "completion",
          title: "Funded Account Challenge Completion",
          description: "Successfully completed Phase 1 funded account challenge",
          status: "active",
          issueDate: "2024-02-20",
          issuer: "Funded Horizon",
          level: "intermediate",
          score: 88,
          verificationUrl: "https://verify.fundedhorizon.com/cert/FH-CERT-2024-002",
          downloadUrl: "/api/certificates/FH-CERT-2024-002/download",
          metadata: {
            program: "Phase 1 Challenge",
            duration: "30 days",
            requirements: ["Meet profit targets", "Stay within drawdown limits", "Follow trading rules"],
            achievements: ["Consistent Performance", "Risk Discipline", "Professional Trading"]
          }
        },
        {
          id: "3",
          certificateId: "FH-CERT-2024-003",
          type: "achievement",
          title: "Risk Management Excellence",
          description: "Demonstrated exceptional risk management skills",
          status: "active",
          issueDate: "2024-03-10",
          issuer: "Funded Horizon",
          level: "expert",
          score: 100,
          verificationUrl: "https://verify.fundedhorizon.com/cert/FH-CERT-2024-003",
          downloadUrl: "/api/certificates/FH-CERT-2024-003/download",
          metadata: {
            program: "Risk Management Specialization",
            duration: "3 months",
            requirements: ["Zero major drawdowns", "Consistent position sizing", "Advanced risk metrics"],
            achievements: ["Perfect Risk Score", "Industry Recognition", "Mentor Status"]
          }
        }
      ]
      
      setCertificates(mockCertificates)
    } catch (error) {
      console.error('Error fetching certificates:', error)
      toast.error('Failed to load certificates')
    } finally {
      setIsLoading(false)
    }
  }

  const filterCertificates = () => {
    let filtered = certificates

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(cert =>
        cert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.certificateId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (typeFilter !== "all") {
      filtered = filtered.filter(cert => cert.type === typeFilter)
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(cert => cert.status === statusFilter)
    }

    // Level filter
    if (levelFilter !== "all") {
      filtered = filtered.filter(cert => cert.level === levelFilter)
    }

    setFilteredCertificates(filtered)
  }

  const verifyCertificate = async () => {
    if (!verificationId.trim()) {
      toast.error('Please enter a certificate ID')
      return
    }

    setIsVerifying(true)
    try {
      // Mock verification - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const foundCertificate = certificates.find(cert => 
        cert.certificateId.toLowerCase() === verificationId.toLowerCase()
      )

      if (foundCertificate) {
        setVerificationResult({
          isValid: true,
          certificate: foundCertificate,
          message: 'Certificate verified successfully!',
          verifiedAt: new Date().toISOString()
        })
        toast.success('Certificate verified successfully!')
      } else {
        setVerificationResult({
          isValid: false,
          message: 'Certificate not found or invalid',
          verifiedAt: new Date().toISOString()
        })
        toast.error('Certificate not found or invalid')
      }
    } catch (error) {
      setVerificationResult({
        isValid: false,
        message: 'Verification failed. Please try again.',
        verifiedAt: new Date().toISOString()
      })
      toast.error('Verification failed. Please try again.')
    } finally {
      setIsVerifying(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const downloadCertificate = async (certificate: Certificate) => {
    try {
      // Mock download - replace with actual API call
      toast.info('Downloading certificate...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Certificate downloaded successfully!')
    } catch (error) {
      toast.error('Download failed')
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      expired: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle },
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock },
      revoked: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getTypeBadge = (type: string) => {
    const config = {
      trading: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", label: "Trading" },
      completion: { color: "bg-green-500/15 text-green-400 border-green-500/25", label: "Completion" },
      achievement: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", label: "Achievement" },
      verification: { color: "bg-orange-500/15 text-orange-400 border-orange-500/25", label: "Verification" }
    }

    const badgeConfig = config[type as keyof typeof config]

    return (
      <Badge className={`${badgeConfig.color} border`}>
        {badgeConfig.label}
      </Badge>
    )
  }

  const getLevelBadge = (level: string) => {
    const config = {
      beginner: { color: "bg-gray-500/15 text-gray-400 border-gray-500/25", label: "Beginner" },
      intermediate: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", label: "Intermediate" },
      advanced: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", label: "Advanced" },
      expert: { color: "bg-orange-500/15 text-orange-400 border-orange-500/25", label: "Expert" }
    }

    const badgeConfig = config[level as keyof typeof config]

    return (
      <Badge className={`${badgeConfig.color} border`}>
        {badgeConfig.label}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading certificates...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-6xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className="text-3xl font-bold text-slate-100">Certificates</h1>
              <p className="text-slate-400 mt-1">Manage and verify your trading certificates</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge className="bg-blue-500/15 text-blue-400 border-blue-500/25">
                <Award size={14} className="mr-1" />
                {certificates.length} Certificates
              </Badge>
            </div>
          </motion.div>

          {/* Certificate Verification */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Shield size={20} />
                Certificate Verification
              </CardTitle>
              <CardDescription className="text-slate-400">
                Verify any certificate by entering its unique ID
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Enter certificate ID (e.g., FH-CERT-2024-001)"
                    value={verificationId}
                    onChange={(e) => setVerificationId(e.target.value)}
                    className="bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
                <Button
                  onClick={verifyCertificate}
                  disabled={isVerifying}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isVerifying ? 'Verifying...' : 'Verify Certificate'}
                </Button>
              </div>
              
              {verificationResult && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`mt-4 p-4 rounded-lg border ${
                    verificationResult.isValid 
                      ? 'bg-green-500/10 border-green-500/25' 
                      : 'bg-red-500/10 border-red-500/25'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {verificationResult.isValid ? (
                      <CheckCircle size={20} className="text-green-400" />
                    ) : (
                      <XCircle size={20} className="text-red-400" />
                    )}
                    <div>
                      <p className={`font-medium ${verificationResult.isValid ? 'text-green-400' : 'text-red-400'}`}>
                        {verificationResult.message}
                      </p>
                      {verificationResult.certificate && (
                        <div className="mt-2 space-y-1">
                          <p className="text-sm text-slate-300">
                            <strong>Title:</strong> {verificationResult.certificate.title}
                          </p>
                          <p className="text-sm text-slate-300">
                            <strong>Issued:</strong> {formatDate(verificationResult.certificate.issueDate)}
                          </p>
                          <p className="text-sm text-slate-300">
                            <strong>Issuer:</strong> {verificationResult.certificate.issuer}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>

          {/* Filters */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Filter size={20} />
                Filters & Search
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
                  <Input
                    placeholder="Search certificates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
                
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="trading">Trading</SelectItem>
                    <SelectItem value="completion">Completion</SelectItem>
                    <SelectItem value="achievement">Achievement</SelectItem>
                    <SelectItem value="verification">Verification</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="revoked">Revoked</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={levelFilter} onValueChange={setLevelFilter}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                    <SelectValue placeholder="Level" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Certificates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCertificates.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <Award size={48} className="text-slate-400 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">No certificates found</p>
                <p className="text-slate-500 text-sm">Try adjusting your filters or search terms</p>
              </div>
            ) : (
              filteredCertificates.map((certificate, index) => (
                <motion.div
                  key={certificate.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 h-full">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            {getTypeBadge(certificate.type)}
                            {getStatusBadge(certificate.status)}
                          </div>
                          <CardTitle className="text-slate-100 text-lg">{certificate.title}</CardTitle>
                          <CardDescription className="text-slate-400 mt-1">
                            {certificate.description}
                          </CardDescription>
                        </div>
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center border border-blue-500/30">
                          <Award size={24} className="text-blue-400" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">Certificate ID:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-slate-200 font-mono">{certificate.certificateId}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(certificate.certificateId)}
                              className="text-slate-400 hover:text-slate-200 p-1"
                            >
                              <Copy size={14} />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">Level:</span>
                          {getLevelBadge(certificate.level)}
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">Issued:</span>
                          <span className="text-slate-200">{formatDate(certificate.issueDate)}</span>
                        </div>
                        
                        {certificate.expiryDate && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Expires:</span>
                            <span className="text-slate-200">{formatDate(certificate.expiryDate)}</span>
                          </div>
                        )}
                        
                        {certificate.score && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Score:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-slate-200">{certificate.score}%</span>
                              <Star size={14} className="text-yellow-400 fill-current" />
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex gap-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedCertificate(certificate)}
                          className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Eye size={14} className="mr-2" />
                          View Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => downloadCertificate(certificate)}
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        >
                          <Download size={14} />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </div>

          {/* Certificate Details Dialog */}
          <Dialog open={!!selectedCertificate} onOpenChange={() => setSelectedCertificate(null)}>
            <DialogContent className="bg-slate-800 border-slate-700 max-w-2xl">
              {selectedCertificate && (
                <>
                  <DialogHeader>
                    <DialogTitle className="text-slate-100 flex items-center gap-2">
                      <Award size={20} />
                      Certificate Details
                    </DialogTitle>
                    <DialogDescription className="text-slate-400">
                      Complete information about {selectedCertificate.title}
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-6">
                    {/* Header Info */}
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-xl font-bold text-slate-100">{selectedCertificate.title}</h3>
                        <p className="text-slate-400 mt-1">{selectedCertificate.description}</p>
                      </div>
                      <div className="flex gap-2">
                        {getTypeBadge(selectedCertificate.type)}
                        {getStatusBadge(selectedCertificate.status)}
                      </div>
                    </div>
                    
                    {/* Certificate Details */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Certificate ID:</span>
                          <span className="text-slate-200 font-mono">{selectedCertificate.certificateId}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Issuer:</span>
                          <span className="text-slate-200">{selectedCertificate.issuer}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Level:</span>
                          {getLevelBadge(selectedCertificate.level)}
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Issue Date:</span>
                          <span className="text-slate-200">{formatDate(selectedCertificate.issueDate)}</span>
                        </div>
                        {selectedCertificate.expiryDate && (
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Expiry Date:</span>
                            <span className="text-slate-200">{formatDate(selectedCertificate.expiryDate)}</span>
                          </div>
                        )}
                        {selectedCertificate.score && (
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Score:</span>
                            <span className="text-slate-200">{selectedCertificate.score}%</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Program Details */}
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-slate-100">Program Details</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-slate-400">Program</p>
                          <p className="text-slate-200">{selectedCertificate.metadata.program}</p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">Duration</p>
                          <p className="text-slate-200">{selectedCertificate.metadata.duration}</p>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Requirements</p>
                        <ul className="space-y-1">
                          {selectedCertificate.metadata.requirements.map((req, index) => (
                            <li key={index} className="text-sm text-slate-200 flex items-center gap-2">
                              <CheckCircle size={14} className="text-green-400" />
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Achievements</p>
                        <ul className="space-y-1">
                          {selectedCertificate.metadata.achievements.map((achievement, index) => (
                            <li key={index} className="text-sm text-slate-200 flex items-center gap-2">
                              <Star size={14} className="text-yellow-400" />
                              {achievement}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex gap-3 pt-4 border-t border-slate-700">
                      <Button
                        onClick={() => window.open(selectedCertificate.verificationUrl, '_blank')}
                        className="flex-1 bg-blue-600 hover:bg-blue-700"
                      >
                        <Verified size={16} className="mr-2" />
                        Verify Online
                      </Button>
                      <Button
                        onClick={() => downloadCertificate(selectedCertificate)}
                        variant="outline"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Download size={16} className="mr-2" />
                        Download
                      </Button>
                      <Button
                        onClick={() => copyToClipboard(selectedCertificate.certificateId)}
                        variant="outline"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Copy size={16} className="mr-2" />
                        Copy ID
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  )
} 