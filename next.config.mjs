let userConfig;
try {
  userConfig = await import('./v0-user-next.config');
} catch (e) {
  // Ignore error if the user config file doesn't exist
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone', // Required for Heroku and similar deployments
  reactStrictMode: true, // Helps catch React issues
  trailingSlash: false, // Changed to false to better handle query parameters
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  compress: true, // Enables gzip and brotli compression
  images: {
    unoptimized: false, // Enables Next.js image optimizations (requires 'sharp' package)
    formats: ['image/avif', 'image/webp'], // Faster image formats
    // Alternative: Set unoptimized: true to disable image optimization and avoid sharp dependency
    domains: ['fundedhorizon-back-e4285707ccdf.herokuapp.com'], // Allow images from backend
  },
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        // Security Headers
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'X-Frame-Options',
          value: 'DENY',
        },
        {
          key: 'X-XSS-Protection',
          value: '1; mode=block',
        },
        {
          key: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin',
        },
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains; preload',
        },
        {
          key: 'Content-Security-Policy',
          value: [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net https://www.googletagmanager.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https: blob: https://fundedhorizon-back-e4285707ccdf.herokuapp.com",
            "connect-src 'self' https://fundedhorizon-back-e4285707ccdf.herokuapp.com https://www.facebook.com",
            "frame-src 'self' https://www.facebook.com",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests"
          ].join('; '),
        },
        {
          key: 'Permissions-Policy',
          value: 'camera=(), microphone=(), geolocation=(), payment=()',
        },
        // Cache Control
        {
          key: 'Cache-Control',
          value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate',
        },
      ],
    },
    // API routes - no caching
    {
      source: '/api/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
        },
        {
          key: 'Pragma',
          value: 'no-cache',
        },
        {
          key: 'Expires',
          value: '0',
        },
      ],
    },
  ],

  // Add rewrites to handle reset-password with query parameters
  rewrites: async () => [
    {
      source: '/reset-password',
      destination: '/reset-password',
    },
    {
      source: '/reset-password/:path*',
      destination: '/reset-password',
    },
  ],
  experimental: {
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
    optimizeCss: true,
    serverMinification: true,
    serverSourceMaps: true,
    optimizeServerReact: true,
  },
  // Security: Disable source maps in production
  productionBrowserSourceMaps: false,
  // Security: Disable directory listing
  generateEtags: false,
};

// Merge user configuration if it exists
if (userConfig) {
  for (const key in userConfig) {
    if (typeof nextConfig[key] === 'object' && !Array.isArray(nextConfig[key])) {
      nextConfig[key] = { ...nextConfig[key], ...userConfig[key] };
    } else {
      nextConfig[key] = userConfig[key];
    }
  }
}

export default nextConfig;
