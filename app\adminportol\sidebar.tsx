import { useRouter } from "next/navigation"
import secureStorageAPI from '@/app/lib/secureStorage';

export default function AdminSidebar() {
  const router = useRouter()
  const handleLogout = () => {
    secureStorageAPI.removeItem("admin-token")
    router.push("/adminportol/login")
  }
  // You can expand this with more admin info if needed
  return (
    <aside className="p-4 border-r border-gray-800/50 min-h-screen bg-[#0d2339]/80">
      <div className="mb-4 text-white font-bold">Admin Panel</div>
      <button onClick={handleLogout} className="text-red-400 hover:underline">Logout</button>
    </aside>
  )
}
