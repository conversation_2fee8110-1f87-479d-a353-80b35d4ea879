import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, RefreshCw, Eye, Plus } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { getApiUrl, getEndpointPath } from "../app/config/api"

interface Certificate {
  certificate_number: string
  order_id: string
  username: string
  issue_date: string
  account_size: string
  challenge_type: string
  profit_target: number
}

// Mock data for certificates
const mockCertificates: Certificate[] = [
  {
    certificate_number: "CERT-2024-001",
    order_id: "FDH1001",
    username: "<PERSON>",
    issue_date: "2024-03-15",
    account_size: "$100,000",
    challenge_type: "High-Risk",
    profit_target: 50
  },
  {
    certificate_number: "CERT-2024-002",
    order_id: "FDH1002",
    username: "Jane Smith",
    issue_date: "2024-03-16",
    account_size: "$150,000",
    challenge_type: "Medium-Risk",
    profit_target: 30
  },
  {
    certificate_number: "CERT-2024-003",
    order_id: "FDH1003",
    username: "Bob Johnson",
    issue_date: "2024-03-17",
    account_size: "$200,000",
    challenge_type: "Low-Risk",
    profit_target: 10
  },
  {
    certificate_number: "CERT-2024-004",
    order_id: "FDH1004",
    username: "Alice Brown",
    issue_date: "2024-03-18",
    account_size: "$250,000",
    challenge_type: "High-Risk",
    profit_target: 60
  },
  {
    certificate_number: "CERT-2024-005",
    order_id: "FDH1005",
    username: "Charlie Davis",
    issue_date: "2024-03-19",
    account_size: "$300,000",
    challenge_type: "Medium-Risk",
    profit_target: 40
  }
];

interface CertificatePreviewModalProps {
  certificate: Certificate | null
  onClose: () => void
}

const CertificatePreviewModal = ({ certificate, onClose }: CertificatePreviewModalProps) => {
  if (!certificate) return null

  return (
    <Dialog open={!!certificate} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl bg-gradient-to-b from-gray-900 to-gray-950 border border-orange-800/30 shadow-2xl rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white">
            Certificate Preview - {certificate.certificate_number}
          </DialogTitle>
        </DialogHeader>
        <div className="mt-4 aspect-[1.414] w-full bg-white rounded-lg overflow-hidden">
          {/* For mock data, show a placeholder */}
          <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-500">
            <div className="text-center">
              <p className="font-bold mb-2">Certificate Preview</p>
              <p className="text-sm">Order ID: {certificate.order_id}</p>
              <p className="text-sm">Certificate Number: {certificate.certificate_number}</p>
              <p className="text-sm">Date: {new Date(certificate.issue_date).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface AddCertificateModalProps {
  isOpen: boolean
  onClose: () => void
  onAdd: (orderId: string) => void
}

const AddCertificateModal = ({ isOpen, onClose, onAdd }: AddCertificateModalProps) => {
  const [orderId, setOrderId] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async () => {
    if (!orderId) {
      toast({
        title: "Error!",
        description: "Please enter an Order ID",
        className: "bg-red-900/50 text-white border-red-500/50 backdrop-blur-sm",
      })
      return
    }

    // Remove 'FDH' prefix if present and clean the order ID
    const cleanOrderId = orderId.replace(/^FDH/i, '').trim()

    setIsLoading(true)
    try {
      const response = await fetch(getApiUrl(`order/send_certificate/${cleanOrderId}`), {
        method: 'POST',
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to create certificate')
      }

      // Show success message
      toast({
        title: "Success!",
        description: "Certificate created successfully",
        className: "bg-green-900/50 text-white border-green-500/50 backdrop-blur-sm",
        duration: 3000,
      })

      // Clear input and close modal
      setOrderId("")
      onClose()
      
      // Trigger refresh of certificates
      onAdd(orderId)
    } catch (error) {
      console.error('Error creating certificate:', error)
      toast({
        title: "Error!",
        description: error instanceof Error ? error.message : "Failed to create certificate. Please try again.",
        className: "bg-red-900/50 text-white border-red-500/50 backdrop-blur-sm",
        duration: 5000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gradient-to-b from-gray-900 to-gray-950 border border-blue-800/30 shadow-2xl rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold text-white">
            Add New Certificate
          </DialogTitle>
        </DialogHeader>
        <div className="mt-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="orderId" className="text-sm text-gray-200">Order ID</Label>
            <Input
              id="orderId"
              placeholder="Enter Order ID (e.g., FDH1001)"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              className="bg-gray-800/50 border-gray-700 text-white"
              disabled={isLoading}
            />
          </div>
        </div>
        <DialogFooter className="mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-gray-800 text-white border-gray-700 hover:bg-gray-700"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-blue-600 text-white hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Certificate'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function CertificateTable() {
  const [certificates, setCertificates] = useState<Certificate[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedCertificate, setSelectedCertificate] = useState<Certificate | null>(null)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)

  const fetchCertificates = async () => {
    try {
      setIsRefreshing(true)
      const response = await fetch(getApiUrl('order/certificates'))
      if (!response.ok) {
        throw new Error("Failed to fetch certificates")
      }
      const data = await response.json()
      setCertificates(data)
      toast({
        title: "Success",
        description: "Certificates refreshed successfully",
        className: "bg-green-900/50 text-white border-green-500/50 backdrop-blur-sm",
        duration: 3000,
      })
    } catch (error) {
      console.error("Error fetching certificates:", error)
      toast({
        title: "Error",
        description: "Failed to fetch certificates. Please try again.",
        className: "bg-red-900/50 text-white border-red-500/50 backdrop-blur-sm",
        duration: 5000,
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchCertificates()
  }, [])

  const filteredCertificates = certificates.filter(cert => 
    cert.certificate_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.order_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.challenge_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.account_size.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPages = Math.ceil(filteredCertificates.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentCertificates = filteredCertificates.slice(startIndex, endIndex)

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  const handleAddCertificate = async (orderId: string) => {
    // After successful certificate creation, refresh the certificates list
    await fetchCertificates()
  }

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Certificates</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-blue-600 text-white hover:bg-blue-700 h-8 px-3 text-xs"
              size="sm"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Certificate
            </Button>
            <Button
              onClick={fetchCertificates}
              disabled={isRefreshing}
              className="h-8 w-8 p-0"
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search certificates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Certificate Number</TableHead>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Username</TableHead>
                  <TableHead className="text-xs">Issue Date</TableHead>
                  <TableHead className="text-xs">Account Size</TableHead>
                  <TableHead className="text-xs">Challenge Type</TableHead>
                  <TableHead className="text-xs">Profit Target</TableHead>
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentCertificates.map((cert) => (
                  <TableRow key={cert.certificate_number}>
                    <TableCell className="text-xs">{cert.certificate_number}</TableCell>
                    <TableCell className="text-xs">{cert.order_id}</TableCell>
                    <TableCell className="text-xs">{cert.username}</TableCell>
                    <TableCell className="text-xs">{new Date(cert.issue_date).toLocaleDateString()}</TableCell>
                    <TableCell className="text-xs">${cert.account_size}</TableCell>
                    <TableCell className="text-xs">{cert.challenge_type}</TableCell>
                    <TableCell className="text-xs">{cert.profit_target}%</TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCertificate(cert)}
                        className="bg-green-500 text-white hover:bg-green-600 rounded-lg text-xs"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        Preview
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Pagination Controls */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-2">
                <select 
                  value={itemsPerPage} 
                  onChange={(e) => setItemsPerPage(Number(e.target.value))}
                  className="bg-[#1E3A5F]/20 border border-[#1E3A5F] text-white rounded-lg text-xs px-2 py-1"
                >
                  <option value={10}>10 per page</option>
                  <option value={25}>25 per page</option>
                  <option value={50}>50 per page</option>
                  <option value={100}>100 per page</option>
                </select>
                <span className="text-gray-400 text-xs">
                  Showing {startIndex + 1} to {Math.min(endIndex, filteredCertificates.length)} of {filteredCertificates.length} entries
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="bg-[#1E3A5F]/20 border-[#1E3A5F] text-white hover:bg-[#1E3A5F]/40 rounded-lg text-xs px-3"
                >
                  Previous
                </Button>
                <span className="text-white text-xs font-medium">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="bg-[#1E3A5F]/20 border-[#1E3A5F] text-white hover:bg-[#1E3A5F]/40 rounded-lg text-xs px-3"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <CertificatePreviewModal
        certificate={selectedCertificate}
        onClose={() => setSelectedCertificate(null)}
      />

      <AddCertificateModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddCertificate}
      />
    </motion.div>
  )
} 