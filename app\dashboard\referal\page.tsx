"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Users, 
  UserPlus, 
  T<PERSON>dingUp, 
  DollarSign, 
  Copy, 
  Share2, 
  Award,
  Calendar,
  CheckCircle,
  Clock,
  Star,
  Gift,
  BarChart3,
  Target,
  Zap,
  CreditCard,
  Coins,
  Wallet
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { toast } from "react-toastify"
import { getApiUrl, getEndpointPath } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

interface Referral {
  id: string
  name: string
  email: string
  status: 'pending' | 'active' | 'completed' | 'inactive'
  joinDate: string
  lastActivity: string
  totalEarnings: number
  commission: number
  level: 'bronze' | 'silver' | 'gold' | 'platinum'
  avatar: string
}

interface ReferralStats {
  totalReferrals: number
  activeReferrals: number
  totalEarnings: number
  thisMonthEarnings: number
  pendingReferrals: number
  conversionRate: number
  averageCommission: number
  totalPoints: number
  availableBalance: number
}

interface ReferralTier {
  name: string
  requirements: number
  commission: number
  rewards: string[]
  current: boolean
  achieved: boolean
}

interface UserProfile {
  id: number
  username: string
  email: string
  name: string
  country: string
  phone_no: string
  address: string
  created_at: string
  referred_by: string | null
  user_referral: {
    id: number
    user_id: number
    referral_code: string
    total_points: number
    created_at: string
    updated_at: string
  }
  is_verified: boolean
}

export default function ReferralPage() {
  const [referrals, setReferrals] = useState<Referral[]>([])
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    activeReferrals: 0,
    totalEarnings: 0,
    thisMonthEarnings: 0,
    pendingReferrals: 0,
    conversionRate: 0,
    averageCommission: 0,
    totalPoints: 0,
    availableBalance: 0
  })
  const [referralLink, setReferralLink] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [selectedFilter, setSelectedFilter] = useState<string>("all")

  const referralTiers: ReferralTier[] = [
    {
      name: "Bronze",
      requirements: 0,
      commission: 5,
      rewards: ["5% commission", "Basic rewards", "Earn points on every referral"],
      current: true,
      achieved: true
    },
    {
      name: "Silver",
      requirements: 5,
      commission: 7,
      rewards: ["7% commission", "Priority support", "Exclusive content", "Bonus points"],
      current: false,
      achieved: true
    },
    {
      name: "Gold",
      requirements: 15,
      commission: 10,
      rewards: ["10% commission", "VIP support", "Early access", "Custom rewards", "Premium points"],
      current: false,
      achieved: false
    },
    {
      name: "Platinum",
      requirements: 30,
      commission: 15,
      rewards: ["15% commission", "Dedicated manager", "Exclusive events", "Premium rewards", "Maximum points"],
      current: false,
      achieved: false
    }
  ]

  useEffect(() => {
    fetchUserProfile()
    fetchReferralData()
  }, [])

  const fetchUserProfile = async () => {
    try {
      const token = await secureStorageAPI.getItem('access_token')
      
      if (!token) {
        console.error('No access token found')
        return
      }

      const response = await fetch(getApiUrl('auth/user/me'), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data: UserProfile = await response.json()
        setUserProfile(data)
        
        // Set referral link with user's actual referral code
        if (data.user_referral?.referral_code) {
          setReferralLink(`https://fundedhorizon.com/ref/${data.user_referral.referral_code}`)
        }
        
        // Update stats with user's actual points
        setStats(prev => ({
          ...prev,
          totalPoints: data.user_referral?.total_points || 0
        }))
      } else {
        console.error('Failed to fetch profile data')
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  const fetchReferralData = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockReferrals: Referral[] = [
        {
          id: "1",
          name: "John Smith",
          email: "<EMAIL>",
          status: "active",
          joinDate: "2024-01-15",
          lastActivity: "2024-03-20",
          totalEarnings: 1250,
          commission: 62.50,
          level: "silver",
          avatar: ""
        },
        {
          id: "2",
          name: "Sarah Johnson",
          email: "<EMAIL>",
          status: "active",
          joinDate: "2024-02-10",
          lastActivity: "2024-03-19",
          totalEarnings: 890,
          commission: 44.50,
          level: "bronze",
          avatar: ""
        },
        {
          id: "3",
          name: "Mike Wilson",
          email: "<EMAIL>",
          status: "pending",
          joinDate: "2024-03-15",
          lastActivity: "2024-03-18",
          totalEarnings: 0,
          commission: 0,
          level: "bronze",
          avatar: ""
        },
        {
          id: "4",
          name: "Emily Davis",
          email: "<EMAIL>",
          status: "completed",
          joinDate: "2024-01-20",
          lastActivity: "2024-03-15",
          totalEarnings: 2100,
          commission: 105,
          level: "gold",
          avatar: ""
        }
      ]

      setReferrals(mockReferrals)
      
      // Calculate stats
      const totalReferrals = mockReferrals.length
      const activeReferrals = mockReferrals.filter(r => r.status === 'active').length
      const totalEarnings = mockReferrals.reduce((sum, r) => sum + r.totalEarnings, 0)
      const thisMonthEarnings = mockReferrals.reduce((sum, r) => sum + r.commission, 0)
      const pendingReferrals = mockReferrals.filter(r => r.status === 'pending').length
      const conversionRate = (activeReferrals / totalReferrals) * 100
      const averageCommission = totalEarnings / totalReferrals

      setStats(prev => ({
        ...prev,
        totalReferrals,
        activeReferrals,
        totalEarnings,
        thisMonthEarnings,
        pendingReferrals,
        conversionRate,
        averageCommission,
        availableBalance: totalEarnings
      }))
    } catch (error) {
      console.error('Error fetching referral data:', error)
      toast.error('Failed to load referral data')
    } finally {
      setIsLoading(false)
    }
  }

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink)
    toast.success('Referral link copied to clipboard!')
  }

  const copyReferralCode = () => {
    const referralCode = userProfile?.user_referral?.referral_code
    if (referralCode) {
      navigator.clipboard.writeText(referralCode)
      toast.success('Referral code copied to clipboard!')
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock },
      completed: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Award },
      inactive: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: Users }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getLevelBadge = (level: string) => {
    const config = {
      bronze: { color: "bg-amber-500/15 text-amber-400 border-amber-500/25", label: "Bronze" },
      silver: { color: "bg-gray-500/15 text-gray-400 border-gray-500/25", label: "Silver" },
      gold: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", label: "Gold" },
      platinum: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", label: "Platinum" }
    }

    const badgeConfig = config[level as keyof typeof config]

    return (
      <Badge className={`${badgeConfig.color} border`}>
        {badgeConfig.label}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const filteredReferrals = selectedFilter === "all" 
    ? referrals 
    : referrals.filter(ref => ref.status === selectedFilter)

  if (isLoading) {
  return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading referral data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-950 via-slate-900 to-orange-950 text-white overflow-x-hidden">
      {/* Animated background shapes */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-orange-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tr from-orange-500/20 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-full blur-3xl animate-pulse delay-500 -translate-x-1/2 -translate-y-1/2" />
      </div>
      <div className="container mx-auto px-4 py-8 md:py-16 relative z-10">
        <div className="max-w-6xl mx-auto shadow-2xl rounded-3xl bg-gradient-to-br from-blue-900/80 via-slate-900/80 to-orange-900/80 border-2 border-blue-500/20 backdrop-blur-xl p-2 md:p-4">
          <div className="p-6 space-y-6">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-between"
            >
              <div>
                <h1 className="text-3xl font-bold text-slate-100">Referral Program</h1>
                <p className="text-slate-400 mt-1">Earn rewards by inviting friends to join Funded Horizon</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">
                  <Users size={14} className="mr-1" />
                  {stats.totalReferrals} Referrals
                </Badge>
              </div>
            </motion.div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Total Referrals</p>
                        <p className="text-2xl font-bold text-slate-100">{stats.totalReferrals}</p>
                        <p className="text-xs text-slate-500 mt-1">All time</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                        <Users size={24} className="text-blue-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Total Earnings</p>
                        <p className="text-2xl font-bold text-orange-400">{formatCurrency(stats.totalEarnings)}</p>
                        <p className="text-xs text-slate-500 mt-1">All time</p>
                      </div>
                      <div className="w-12 h-12 bg-orange-500/15 rounded-lg flex items-center justify-center">
                        <DollarSign size={24} className="text-orange-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Total Points</p>
                        <p className="text-2xl font-bold text-yellow-400">{stats.totalPoints}</p>
                        <p className="text-xs text-slate-500 mt-1">Earned points</p>
                      </div>
                      <div className="w-12 h-12 bg-yellow-500/15 rounded-lg flex items-center justify-center">
                        <Coins size={24} className="text-yellow-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                  </motion.div>
              </div>

            {/* Referral Link Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="bg-gradient-to-r from-blue-500/10 to-orange-500/10 border-blue-500/20">
                <CardHeader>
                  <CardTitle className="text-slate-100 flex items-center gap-2">
                    <Share2 size={20} />
                    Your Referral Code
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Share this code with friends to earn rewards when they join
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-3">
                    <div className="flex-1">
                      <Input
                        value={userProfile?.user_referral?.referral_code || ''}
                        readOnly
                        className="bg-slate-700/50 border-slate-600 text-slate-100 font-mono text-sm text-center"
                        placeholder="Loading referral code..."
                      />
                    </div>
                    <Button
                      onClick={copyReferralCode}
                      variant="outline"
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Copy size={16} className="mr-2" />
                      Copy Code
                    </Button>
                    <Button
                      onClick={copyReferralLink}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Share2 size={16} className="mr-2" />
                      Copy Link
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

              {/* Referral Tiers */}
              <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              >
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-slate-100 flex items-center gap-2">
                      <Award size={20} />
                      Referral Tiers
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Unlock higher commissions and rewards
                    </CardDescription>
                  </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {referralTiers.map((tier, index) => (
                      <div
                        key={tier.name}
                        className={`p-4 rounded-lg border transition-all duration-300 ${
                          tier.current
                            ? 'bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/30'
                            : tier.achieved
                            ? 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-500/30'
                            : 'bg-slate-700/30 border-slate-600/50'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className={`font-semibold ${
                            tier.current ? 'text-orange-400' : tier.achieved ? 'text-blue-400' : 'text-slate-400'
                          }`}>
                            {tier.name}
                          </h4>
                          <div className="flex items-center gap-2">
                            {tier.current && <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">Current</Badge>}
                            {tier.achieved && !tier.current && <CheckCircle size={16} className="text-blue-400" />}
                          </div>
                        </div>
                        <p className="text-sm text-slate-300 mb-2">
                          {tier.requirements} referrals • {tier.commission}% commission
                        </p>
                        <ul className="space-y-1">
                          {tier.rewards.map((reward, idx) => (
                            <li key={idx} className="text-xs text-slate-400 flex items-center gap-2">
                              <Star size={12} className="text-orange-400" />
                              {reward}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}

