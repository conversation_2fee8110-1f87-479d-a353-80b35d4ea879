"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Activity, 
  DollarSign, 
  Percent, 
  <PERSON><PERSON><PERSON>riangle,
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  Clock,
  BarChart3,
  Shield,
  Award
} from "lucide-react"
import { useRouter } from 'next/navigation'
import { ResponsiveSankey } from '@nivo/sankey'
import { secureFetch, API_CONFIG } from '@/app/config/api'
import secureStorageAPI from '@/app/lib/secureStorage'

import { Header } from "@/components/header"
import { OverviewCard } from "@/components/overview-card"
import { AccountDetailsCard } from "@/components/account-details-card"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import TradingChallenge from '../component/trading-challenge'
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select"

interface TradeDetail {
  openTime: string
  closeTime: string
  symbol: string
  action: string
  sizing: {
    type: string
    value: string
  }
  openPrice: number
  closePrice: number
  profit: number
}

interface AccountDetail {
  balance: number
  equity: number
  totalTrades: number
  drawdown: number
  profit: number
}

interface FormattedTrade {
  id: string
  symbol: string
  type: string
  openPrice: number
  profit: number
  date: string
  volume: number
}

interface PerformanceData {
  period: string
  accountBalance: number
  portfolioEquity: number
}

interface DrawdownData {
  maxDrawdown: number
  currentDrawdown: number
}

interface Order {
  id: string;
  symbol: string;
  action: string;
  sizing: {
    type: string;
    value: string;
  };
  openPrice: number;
  closePrice: number;
  profit: number;
}

export default function DashboardPage() {
  const router = useRouter();
  const [accountDetails, setAccountDetails] = useState<AccountDetail | null>(null)
  const [tradeHistory, setTradeHistory] = useState<FormattedTrade[]>([])
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [drawdownData, setDrawdownData] = useState<DrawdownData>({
    maxDrawdown: 0,
    currentDrawdown: 0,
  })
  const [hasOrders, setHasOrders] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState('');
  const [terminalId, setTerminalId] = useState('');
  const [mounted, setMounted] = useState(false);
  const isMounted = useRef(true);
  const [ordersList, setOrdersList] = useState<any[]>([]);
  const [selectedOrderId, setSelectedOrderId] = useState<string>("");

  // Check for authentication first
  // useEffect(() => {
  //   const token = localStorage.getItem('access_token');
  //   if (!token) {
  //     router.push('/login');
  //   }
  // }, [router]);

  useEffect(() => {
    setMounted(true);
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Check for orders
  useEffect(() => {
    const checkOrders = async () => {
      if (!mounted) return;
      try {
        setIsLoading(true);
        const token = secureStorageAPI.getItem('access_token');
        if (!token) {
          setError('No access token found');
          setHasOrders(false);
          return;
        }
        const response = await secureFetch('/order/order_ids');
        if (!response.ok) {
          setHasOrders(false);
          return;
        }
        const responseData = await response.json();
        let orders = [];
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          orders = responseData.data;
        } else if (Array.isArray(responseData)) {
          orders = responseData;
        } else {
          setHasOrders(false);
          return;
        }
        setOrdersList(orders);
        setHasOrders(orders.length > 0);
        let orderId = secureStorageAPI.getItem('selectedAccountId');
        if (!orderId && orders.length > 0) {
          orderId = orders[0].id || orders[0].order_id;
          if (orderId) {
            secureStorageAPI.setItem('selectedAccountId', orderId.toString());
          }
        }
        setSelectedOrderId(orderId || "");
      } catch (err) {
        setHasOrders(false);
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    };
    checkOrders();
  }, [mounted]);

  // Initialize session and terminal IDs
  useEffect(() => {
    if (mounted) {
      const storedSessionId = secureStorageAPI.getItem('session_id');
      const storedTerminalId = secureStorageAPI.getItem('terminal_id');
      
      if (storedSessionId) setSessionId(storedSessionId);
      if (storedTerminalId) setTerminalId(storedTerminalId);
    }
  }, [mounted]);

  // Fetch data when we have orders
  useEffect(() => {
    if (hasOrders && mounted) {
      fetchData();
    }
  }, [hasOrders, mounted]);

  const LoadingSpinner = () => (
    <div className="min-h-screen flex items-center justify-center">
      <motion.div
        animate={{ 
          scale: [1, 1.2, 1],
          rotate: [0, 360]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"
      />
    </div>
  );

  const fetchData = async () => {
    try {
      if (!isMounted.current) return;

      const formData = new FormData()
      formData.append('session', sessionId)
      formData.append('account_id', terminalId)

      console.log('Fetching MyFXBook data with:', { sessionId, terminalId });

      const response = await secureFetch(API_CONFIG.endpoints.myfxbookFetch, {
        method: 'POST',
        body: formData
      })

      if (!isMounted.current) return;

      if (!response.ok) {
        console.warn('MyFXBook fetch failed, setting default values');
        setAccountDetails({
          balance: 0,
          equity: 0,
          totalTrades: 0,
          drawdown: 0,
          profit: 0,
        });
        setTradeHistory([]);
        setDrawdownData({
          maxDrawdown: 0,
          currentDrawdown: 0,
        });
        setIsLoading(false);
        return;
      }

      const responseData = await response.json()
      const data = responseData.data || responseData;

      console.log('MyFXBook data received:', data);

      if (!isMounted.current) return;

      setAccountDetails({
        balance: data.account_info?.balance || 0,
        equity: data.account_info?.equity || 0,
        totalTrades: data.history?.length || 0,
        drawdown: data.account_info?.drawdown || 0,
        profit: data.account_info?.profit || 0,
      });

      const formattedTrades = (data.history || []).map((trade: TradeDetail) => ({
        id: trade.openTime || '',
        symbol: trade.symbol || '',
        type: trade.action || '',
        openPrice: trade.openPrice || 0,
        profit: trade.profit || 0,
        date: trade.openTime || '',
        volume: trade.sizing?.value || 0
      }));

      if (!isMounted.current) return;

      setTradeHistory(formattedTrades);
      setDrawdownData({
        maxDrawdown: data.account_info?.drawdown || 0,
        currentDrawdown: data.account_info?.drawdown || 0,
      });
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching MyFXBook data:', error);
      if (!isMounted.current) return;

      setAccountDetails({
        balance: 0,
        equity: 0,
        totalTrades: 0,
        drawdown: 0,
        profit: 0,
      });
      setTradeHistory([]);
      setDrawdownData({
        maxDrawdown: 0,
        currentDrawdown: 0,
      });
      setIsLoading(false);
    }
  }

  useEffect(() => {
    const handleStorageChange = () => {
      if (!mounted || !isMounted.current) return;

      const newSessionId = secureStorageAPI.getItem('session_id') || '';
      const newTerminalId = secureStorageAPI.getItem('terminal_id') || '';

      if (newSessionId !== sessionId) {
        setSessionId(newSessionId);
      }
      if (newTerminalId !== terminalId) {
        setTerminalId(newTerminalId);
      }
    };

    if (mounted) {
      // Only listen to storage events from other tabs/windows
      window.addEventListener('storage', handleStorageChange);

      // Check once on mount instead of continuous polling
      handleStorageChange();
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [sessionId, terminalId, mounted]);

  // Add handler for order selection
  const handleOrderSelect = (orderId: string) => {
    setSelectedOrderId(orderId);
    secureStorageAPI.setItem('selectedAccountId', orderId);
    // Optionally, reload data for the selected order
    window.location.reload();
  };

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Only show trading challenge if we explicitly know there are no orders
  if (hasOrders === false) {
    return <TradingChallenge isDashboard={true} />;
  }

  const formatBalance = (balance?: number) => {
    return balance ? `$${balance.toLocaleString()}` : '$0'
  }

  const calculateDrawdown = (drawdown?: number) => {
    return drawdown ? `${drawdown.toFixed(2)}%` : '0%'
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-400 text-center">
          <p className="text-xl font-semibold">Error loading dashboard</p>
          <p className="mt-2">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Order Selection Dropdown */}
      {ordersList.length > 1 && (
        <div className="mb-4 max-w-xs">
          <Select value={selectedOrderId} onValueChange={handleOrderSelect}>
            <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
              <SelectValue placeholder="Select Order" />
            </SelectTrigger>
            <SelectContent className="bg-slate-700 border-slate-600">
              {ordersList.map((order) => (
                <SelectItem key={order.id || order.order_id} value={order.id?.toString() || order.order_id?.toString()}>
                  {order.id || order.order_id}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Trading Dashboard</h1>
          <p className="text-slate-400 mt-1">Monitor your trading performance and account status</p>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6">
        {/* Account Details */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <AccountDetailsCard
            orderId={mounted ? secureStorageAPI.getItem('selectedAccountId') || "" : ""}
          />
        </motion.div>
      </div>
    </div>
  )
}