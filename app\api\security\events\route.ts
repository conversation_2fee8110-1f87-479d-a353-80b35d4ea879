import { NextRequest, NextResponse } from 'next/server';
import { securityMonitor } from '@/app/lib/security-monitoring';
import { validateAuthToken, setSecurityHeaders } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    // Analyze request for security threats
    const threatAnalysis = securityMonitor.analyzeRequest(request);
    if (threatAnalysis.isThreat) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Validate authentication token (admin only)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401, headers: setSecurityHeaders() }
      );
    }

    const token = authHeader.substring(7);
    const tokenValidation = validateAuthToken(token);
    if (!tokenValidation.isValid || !tokenValidation.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403, headers: setSecurityHeaders() }
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const severity = url.searchParams.get('severity');
    const category = url.searchParams.get('category');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Get all events
    let events = securityMonitor.exportEvents();

    // Apply filters
    if (severity) {
      events = events.filter(event => event.severity === severity);
    }

    if (category) {
      events = events.filter(event => event.category === category);
    }

    if (startDate) {
      const start = new Date(startDate);
      events = events.filter(event => new Date(event.timestamp) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      events = events.filter(event => new Date(event.timestamp) <= end);
    }

    // Sort by timestamp (newest first) and limit results
    events = events
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, Math.min(limit, 1000)); // Max 1000 events

    return NextResponse.json({
      events,
      total: events.length,
      filters: {
        severity,
        category,
        startDate,
        endDate,
        limit
      }
    }, {
      status: 200,
      headers: setSecurityHeaders()
    });
  } catch (error) {
    console.error('Security events API error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: setSecurityHeaders() }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      ...setSecurityHeaders(),
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  });
} 