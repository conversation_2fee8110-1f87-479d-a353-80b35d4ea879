"use client";
import { useState, ChangeEvent, FormEvent, useEffect } from "react";
import { Navbar } from "../component/navbar";
import Link from "next/link";
import { motion } from "framer-motion";
import Image from "next/image";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useRouter } from 'next/navigation';
import { 
  Bitcoin, 
  DollarSign, 
  TrendingUp, 
  ArrowUp, 
  ArrowDown, 
  Shield, 
  Lock, 
  Mail, 
  Eye, 
  EyeOff,
  CheckCircle,
  AlertCircle,
  Building2,
  Users,
  Award,
  Zap
} from 'lucide-react';
import { getApiUrl, getEndpointPath } from "../config/api"
import secureStorageAPI from '@/app/lib/secureStorage';
import SecureInput from '@/app/components/SecureInput';

interface LoginData {
  email: string;
  password: string;
}

interface InputFieldProps {
  label: string;
  type: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  name: string;
  className?: string;
  icon?: React.ReactNode;
  error?: string;
  showPasswordToggle?: boolean;
  onTogglePassword?: () => void;
}

function InputField({
  label,
  type,
  placeholder,
  value,
  onChange,
  name,
  className = "",
  icon,
  error,
  showPasswordToggle = false,
  onTogglePassword
}: InputFieldProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col gap-2"
    >
      <label className="text-sm font-semibold text-gray-200 tracking-wide">
        {label}
      </label>
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          name={name}
          className={`
            w-full px-4 py-3.5 rounded-xl bg-gray-900/50 text-white border 
            ${error ? 'border-red-500' : 'border-gray-700'} 
            focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 
            transition-all duration-300 placeholder-gray-500
            ${icon ? 'pl-12' : 'pl-4'}
            ${showPasswordToggle ? 'pr-12' : ''}
            ${className}
          `}
        />
        {showPasswordToggle && (
          <button
            type="button"
            onClick={onTogglePassword}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
          >
            {type === 'password' ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}
      </div>
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-red-400 text-sm"
        >
          <AlertCircle size={16} />
          {error}
        </motion.div>
      )}
    </motion.div>
  );
}

const SecurityBadge = ({ icon: Icon, title, description }: { icon: any, title: string, description: string }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-gray-900/60 to-gray-800/60 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 backdrop-blur-sm relative overflow-hidden group"
  >
    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    <div className="p-2.5 rounded-lg bg-gradient-to-br from-blue-500/20 to-orange-500/20 border border-blue-500/30 group-hover:border-orange-500/30 transition-all duration-300 relative z-10">
      <Icon size={20} className="text-blue-400 group-hover:text-orange-400 transition-colors duration-300" />
    </div>
    <div className="relative z-10">
      <h4 className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">{title}</h4>
      <p className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300">{description}</p>
    </div>
  </motion.div>
);

const TradingPairs = () => {
  const [pairs, setPairs] = useState([
    { pair: 'BTC/USD', price: '43,521.23', change: '+2.34%', isUp: true, volume: '$2.4B', lastUpdate: Date.now() },
    { pair: 'ETH/USD', price: '2,234.56', change: '-1.23%', isUp: false, volume: '$1.8B', lastUpdate: Date.now() },
    { pair: 'SOL/USD', price: '98.45', change: '+5.67%', isUp: true, volume: '$890M', lastUpdate: Date.now() },
    { pair: 'XRP/USD', price: '0.5234', change: '-0.89%', isUp: false, volume: '$450M', lastUpdate: Date.now() },
  ]);

  const [lastUpdate, setLastUpdate] = useState(Date.now());

  const generateRandomChange = (currentPrice: number) => {
    const change = (Math.random() - 0.5) * 0.02;
    const newPrice = currentPrice * (1 + change);
    return {
      price: newPrice.toFixed(2),
      change: `${(change * 100).toFixed(2)}%`,
      isUp: change > 0
    };
  };

  const updatePrices = () => {
    setPairs(prevPairs => 
      prevPairs.map(pair => {
        const currentPrice = parseFloat(pair.price.replace(/,/g, ''));
        const { price, change, isUp } = generateRandomChange(currentPrice);
        
        return {
          ...pair,
          price: parseFloat(price).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
          change,
          isUp,
          lastUpdate: Date.now()
        };
      })
    );
    setLastUpdate(Date.now());
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null
    
    const startInterval = () => {
      intervalId = setInterval(updatePrices, 5000)
    }
    
    startInterval()
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    }
  }, [])

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ago`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-br from-gray-900/90 via-gray-800/85 to-gray-900/90 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 shadow-xl relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-orange-500/5"></div>
      <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-xl"></div>
      <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-orange-500/10 to-transparent rounded-full blur-xl"></div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-100 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-orange-500/20 border border-blue-500/30">
              <TrendingUp size={20} className="text-blue-400" />
            </div>
            Live Market Data
          </h3>
          <span className="text-xs text-gray-400 bg-gray-800/70 px-3 py-1.5 rounded-full border border-gray-700/50">
            {formatTimeAgo(lastUpdate)}
          </span>
        </div>
        <div className="space-y-3">
          {pairs.map((pair, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/15 to-orange-500/15 border border-blue-500/25 group-hover:border-orange-500/25 transition-all duration-300">
                  <Bitcoin size={18} className="text-blue-400 group-hover:text-orange-400 transition-colors duration-300" />
                </div>
                <div>
                  <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">{pair.pair}</span>
                  <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Vol: {pair.volume}</span>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <motion.span 
                  key={pair.price}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="text-gray-300 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300"
                >
                  ${pair.price}
                </motion.span>
                <motion.span 
                  key={pair.change}
                  initial={{ scale: 1.2, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className={`flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
                    pair.isUp 
                      ? 'bg-green-500/15 text-green-400 border border-green-500/25 group-hover:bg-green-500/20' 
                      : 'bg-red-500/15 text-red-400 border border-red-500/25 group-hover:bg-red-500/20'
                  }`}
                >
                  {pair.isUp ? <ArrowUp size={12} /> : <ArrowDown size={12} />}
                  {pair.change}
                </motion.span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

const ProfessionalSpinner = () => (
  <div className="flex items-center justify-center">
    <div className="relative">
      <div className="w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
      <div className="absolute inset-0 w-5 h-5 border-2 border-transparent border-t-blue-400 rounded-full animate-ping"></div>
    </div>
  </div>
);

export default function Login() {
  const router = useRouter();
  const [loginData, setLoginData] = useState<LoginData>({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    
    if (!loginData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(loginData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    
    if (!loginData.password) {
      newErrors.password = "Password is required";
    } else if (loginData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setLoginData({ ...loginData, [e.target.name]: e.target.value });
    // Clear error when user starts typing
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: "" });
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append("username", loginData.email);
      formData.append("password", loginData.password);
      console.log('API Request:', getEndpointPath('auth/login'));
      const response = await fetch(getApiUrl('auth/login'), {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        secureStorageAPI.setItem('access_token', data.access_token);
        secureStorageAPI.setItem('token_type', data.token_type);
        toast.success('Welcome back! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } else {
        const errorData = await response.json();
        toast.error(errorData.detail || 'Invalid credentials. Please check your email and password.');
      }
    } catch (error) {
      toast.error('Connection error. Please check your internet connection.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 p-4 md:p-6 lg:p-8 relative overflow-hidden">
        {/* Enhanced Background Pattern with Orange and Blue - Hidden on mobile */}
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.15),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(249,115,22,0.12),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(59,130,246,0.08),transparent_50%)]"></div>
        <div className="hidden md:block absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(249,115,22,0.1),transparent_50%)]"></div>
        
        {/* Animated Background Elements - Hidden on mobile */}
        <div className="hidden md:block absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-blue-500/3 to-orange-500/3 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>
        
        <div className="w-full max-w-7xl mx-auto mt-20 grid grid-cols-1 lg:grid-cols-2 gap-12 relative z-10">
          {/* Login Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-br from-gray-900/95 via-gray-800/90 to-gray-900/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 md:p-10 lg:p-12 border border-gray-700/50 order-2 lg:order-1 relative overflow-hidden"
          >
            {/* Form Background Pattern - Hidden on mobile */}
            <div className="hidden md:block absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-orange-500/5"></div>
            <div className="hidden md:block absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-transparent rounded-full blur-2xl"></div>
            <div className="hidden md:block absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-orange-500/10 to-transparent rounded-full blur-2xl"></div>
            
            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-10"
              >
                <Image 
                  src="/logo.svg" 
                  alt="Funded Horizon Logo" 
                  width={200} 
                  height={50} 
                  className="mx-auto mb-8 h-auto"
                />
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-100 via-blue-100 to-orange-100 bg-clip-text text-transparent mb-4">
                  Welcome Back
                </h1>
                <p className="text-gray-300 text-lg font-medium">
                  Access your professional trading dashboard
                </p>
              </motion.div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <SecureInput
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email address"
                  value={loginData.email}
                  onChange={handleChange}
                  name="email"
                  icon={<Mail size={20} />}
                  error={errors.email}
                  validation={{
                    required: true,
                    customValidation: (value) => {
                      if (!/\S+@\S+\.\S+/.test(value)) {
                        return { isValid: false, error: 'Please enter a valid email address' };
                      }
                      return { isValid: true };
                    }
                  }}
                />
                
                <SecureInput
                  label="Password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={loginData.password}
                  onChange={handleChange}
                  name="password"
                  icon={<Lock size={20} />}
                  error={errors.password}
                  validation={{
                    required: true,
                    minLength: 6
                  }}
                />

                <motion.div
                  whileHover={{ scale: loading ? 1 : 1.02 }}
                  whileTap={{ scale: loading ? 1 : 0.98 }}
                >
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-blue-600 via-blue-700 to-orange-600 text-white font-bold py-4 rounded-xl hover:from-blue-700 hover:via-blue-800 hover:to-orange-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/25 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center text-lg relative overflow-hidden group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-orange-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative z-10">
                      {loading ? (
                        <>
                          <ProfessionalSpinner />
                          Signing In...
                        </>
                      ) : (
                        'Sign In to Dashboard'
                      )}
                    </span>
                  </button>
                </motion.div>
              </form>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="mt-8 space-y-6"
              >
                <div className="flex items-center justify-between text-sm">
                  <Link href="/forgot-password" className="text-blue-400 hover:text-blue-300 hover:underline transition duration-300 font-medium">
                    Forgot Password?
                  </Link>
                  <Link href="/signup" className="text-blue-400 hover:text-blue-300 hover:underline transition duration-300 font-medium">
                    Create New Account
                  </Link>
                </div>
                
                {/* Security badges - Hidden on mobile */}
                <div className="hidden md:block space-y-3">
                  <SecurityBadge
                    icon={Shield}
                    title="Enterprise Security"
                    description="Bank-level encryption and security protocols"
                  />
                  <SecurityBadge
                    icon={CheckCircle}
                    title="Verified Platform"
                    description="Regulated and compliant trading environment"
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Side Content - Hidden on mobile */}
          <div className="hidden lg:block space-y-8 order-1 lg:order-2">
            <TradingPairs />
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-gray-900/90 via-gray-800/85 to-gray-900/90 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 shadow-xl relative overflow-hidden"
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-blue-500/5"></div>
              <div className="absolute top-0 left-0 w-28 h-28 bg-gradient-to-br from-orange-500/10 to-transparent rounded-full blur-xl"></div>
              <div className="absolute bottom-0 right-0 w-36 h-36 bg-gradient-to-tl from-blue-500/10 to-transparent rounded-full blur-xl"></div>
              
              <div className="relative z-10">
                <h3 className="text-xl font-bold text-gray-100 mb-4 flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-blue-500/20 border border-orange-500/30">
                    <Award size={20} className="text-orange-400" />
                  </div>
                  Platform Highlights
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-green-500/15 to-emerald-500/15 border border-green-500/25 group-hover:border-emerald-500/25 transition-all duration-300">
                      <Users size={18} className="text-green-400 group-hover:text-emerald-400 transition-colors duration-300" />
                    </div>
                    <div>
                      <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">10,000+ Active Traders</span>
                      <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Join our growing community</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-gray-800/40 to-gray-700/40 hover:from-gray-700/50 hover:to-gray-600/50 transition-all duration-300 border border-gray-700/30 hover:border-gray-600/40 backdrop-blur-sm group">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500/15 to-pink-500/15 border border-purple-500/25 group-hover:border-pink-500/25 transition-all duration-300">
                      <Zap size={18} className="text-purple-400 group-hover:text-pink-400 transition-colors duration-300" />
                    </div>
                    <div>
                      <span className="text-gray-200 font-semibold text-sm group-hover:text-gray-100 transition-colors duration-300">Instant Execution</span>
                      <span className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors duration-300 block">Lightning-fast trade processing</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
}
