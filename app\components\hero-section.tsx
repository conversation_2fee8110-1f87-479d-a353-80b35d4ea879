"use client"

import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { TrendingUp, ArrowRight, Shield, Award, Globe, DollarSign, BarChart3, Target } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

// 3D Animated Trading Sphere Component
const TradingSphere3D = ({ isBackground = false }) => {
  return (
    <div className={`relative w-full h-full flex items-center justify-center ${isBackground ? 'absolute inset-0 opacity-30' : ''}`}>
      {/* Main 3D Sphere */}
      <motion.div
        animate={{ 
          rotateY: 360,
          rotateX: 180,
        }}
        transition={{ 
          duration: 20, 
          repeat: Infinity, 
          ease: "linear" 
        }}
        className={`relative ${isBackground ? 'w-96 h-96 md:w-80 md:h-80' : 'w-[28rem] h-[28rem] md:w-96 md:h-96'}`}
        style={{
          transformStyle: 'preserve-3d',
        }}
      >
        {/* Enhanced Sphere layers with more complexity */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute inset-0 rounded-full border-2 ${i % 3 === 0 ? 'border-orange-500/40' : i % 3 === 1 ? 'border-blue-500/40' : 'border-green-500/40'}`}
            style={{
              transform: `rotateY(${i * 30}deg) rotateX(${i * 25}deg) rotateZ(${i * 15}deg)`,
              transformStyle: 'preserve-3d',
            }}
            animate={{
              rotateZ: [0, 360],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 15 + i * 1.5,
              repeat: Infinity,
              ease: "linear",
              delay: i * 0.3,
            }}
          />
        ))}
        
        {/* Inner rotating elements with enhanced effects */}
        <motion.div
          animate={{ 
            rotate: 360,
            scale: [1, 1.05, 1],
          }}
          transition={{ 
            duration: 12, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-8 rounded-full border-2 border-blue-500/50 shadow-lg shadow-blue-500/20"
        />
        
        <motion.div
          animate={{ 
            rotate: -360,
            scale: [1, 0.95, 1],
          }}
          transition={{ 
            duration: 18, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-16 rounded-full border-2 border-green-500/40 shadow-lg shadow-green-500/20"
        />
        
        <motion.div
          animate={{ 
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{ 
            duration: 25, 
            repeat: Infinity, 
            ease: "linear" 
          }}
          className="absolute inset-24 rounded-full border border-purple-500/30 shadow-lg shadow-purple-500/20"
        />
        
        {/* Enhanced floating trading icons with glow effects */}
        <motion.div
          animate={{ 
            y: [0, -25, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 6, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
          className="absolute top-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <DollarSign className={`${isBackground ? 'w-8 h-8' : 'w-8 h-8'} text-orange-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-orange-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.div
          animate={{ 
            y: [0, 25, 0],
            rotate: [0, -360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 8, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-4 left-1/2 -translate-x-1/2"
        >
          <div className="relative">
            <TrendingUp className={`${isBackground ? 'w-8 h-8' : 'w-8 h-8'} text-green-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-green-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.div
          animate={{ 
            x: [0, 25, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 7, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/2 right-4 -translate-y-1/2"
        >
          <div className="relative">
            <BarChart3 className={`${isBackground ? 'w-8 h-8' : 'w-8 h-8'} text-blue-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-blue-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        <motion.div
          animate={{ 
            x: [0, -25, 0],
            rotate: [0, -360],
            scale: [1, 1.2, 1],
          }}
          transition={{ 
            duration: 9, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 3
          }}
          className="absolute top-1/2 left-4 -translate-y-1/2"
        >
          <div className="relative">
            <Target className={`${isBackground ? 'w-8 h-8' : 'w-8 h-8'} text-purple-400 drop-shadow-lg`} />
            <div className="absolute inset-0 w-full h-full bg-purple-400/20 rounded-full blur-md animate-pulse"></div>
          </div>
        </motion.div>
        
        {/* Enhanced central glow effect */}
        <motion.div
          animate={{ 
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.8, 0.3],
          }}
          transition={{ 
            duration: 4, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
          className="absolute inset-20 rounded-full bg-gradient-to-r from-orange-500/30 via-blue-500/30 to-green-500/30 blur-xl"
        />
        
        {/* Additional inner glow layers */}
        <motion.div
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{ 
            duration: 6, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute inset-32 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-lg"
        />
      </motion.div>
      
      {/* Enhanced orbiting particles with trails */}
      {[...Array(16)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute ${isBackground ? 'w-1.5 h-1.5' : 'w-2 h-2'} bg-gradient-to-r from-orange-400 via-blue-400 to-green-400 rounded-full shadow-lg`}
          animate={{
            x: [0, Math.cos(i * 22.5 * Math.PI / 180) * (isBackground ? 150 : 200)],
            y: [0, Math.sin(i * 22.5 * Math.PI / 180) * (isBackground ? 150 : 200)],
            rotate: 360,
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 10 + i * 0.5,
            repeat: Infinity,
            ease: "linear",
            delay: i * 0.15,
          }}
        />
      ))}
      
      {/* Additional floating elements */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={`floating-${i}`}
          className={`absolute ${isBackground ? 'w-1 h-1' : 'w-1.5 h-1.5'} bg-white/60 rounded-full`}
          animate={{
            x: [0, Math.random() * 100 - 50],
            y: [0, Math.random() * 100 - 50],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: i * 0.8,
          }}
        />
      ))}
    </div>
  )
}

// Trading-related animated components
const TradingAnimation = () => {
  const [currentPrice, setCurrentPrice] = useState(1.2345)
  const [isUp, setIsUp] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPrice(prev => {
        const change = (Math.random() - 0.5) * 0.01
        const newPrice = prev + change
        setIsUp(change > 0)
        return parseFloat(newPrice.toFixed(4))
      })
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Animated candlesticks */}
      <div className="absolute top-20 left-10 opacity-20">
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
          className="flex items-end gap-1"
        >
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex flex-col items-center">
              <div className={`w-1 h-${Math.random() * 8 + 4} ${Math.random() > 0.5 ? 'bg-green-500' : 'bg-red-500'} rounded-sm`} />
              <div className={`w-2 h-${Math.random() * 4 + 2} ${Math.random() > 0.5 ? 'bg-green-500' : 'bg-red-500'} rounded-sm mt-1`} />
            </div>
          ))}
        </motion.div>
      </div>

      {/* Floating price indicators */}
      <motion.div
        animate={{ x: [0, 20, 0], y: [0, -10, 0] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute top-32 right-20 bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20"
      >
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isUp ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-white text-sm font-mono">EUR/USD</span>
          <span className={`text-sm font-bold ${isUp ? 'text-green-400' : 'text-red-400'}`}>
            {currentPrice}
          </span>
        </div>
      </motion.div>

      {/* Animated trading chart lines */}
      <svg className="absolute bottom-20 left-20 w-32 h-20 opacity-30" viewBox="0 0 128 80">
        <motion.path
          d="M0,40 Q32,20 64,40 T128,40"
          stroke="url(#gradient)"
          strokeWidth="2"
          fill="none"
          animate={{ d: ["M0,40 Q32,20 64,40 T128,40", "M0,40 Q32,60 64,40 T128,40", "M0,40 Q32,20 64,40 T128,40"] }}
          transition={{ duration: 6, repeat: Infinity }}
        />
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#f97316" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>
      </svg>

      {/* Floating statistics */}
      {/* <motion.div ...> ... $2.5M Max Funding ... </motion.div> */}

      {/* Animated success rate */}
      {/* <motion.div ...> ... 94% Success Rate ... </motion.div> */}

      {/* Animated profit indicator */}
      <motion.div
        animate={{ rotate: [0, 5, -5, 0] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute top-1/2 right-10 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20"
      >
        <div className="flex items-center gap-3">
          <TrendingUp className="w-6 h-6 text-purple-400" />
          <div>
            <div className="text-white font-bold text-lg">90%</div>
            <div className="text-gray-300 text-xs">Profit Split</div>
          </div>
        </div>
      </motion.div>

      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  )
}

export default function Hero() {
  return (
    <section className="relative min-h-screen pt-20 overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36]">
      {/* Trading Animation Background */}
      <TradingAnimation />

      {/* Main Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Mobile Layout - 3D Background behind heading */}
          <div className="lg:hidden relative pt-6 pb-6">
            {/* 3D Background for mobile */}
            <div className="absolute inset-0 flex items-center justify-center">
              <TradingSphere3D isBackground={true} />
            </div>
            
            {/* Content over 3D background */}
            <div className="relative z-10 text-center">
            {/* Single XXL Bold Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
                className="text-4xl sm:text-4xl md:text-6xl font-black leading-tight tracking-tight mb-8 mt-[-2rem] sm:mt-0"
            >
              <span className="text-white block mb-4">
                TRADE
              </span>
              <span className="bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent block">
                LIKE A PRO
              </span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
                className="text-sm sm:text-base md:text-2xl text-gray-300 max-w-2xl mx-auto mb-12 leading-relaxed"
            >
              Get funded up to <span className="text-orange-500 font-bold">$2.5M</span> with our professional trading platform. 
              Join thousands of successful traders worldwide.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-10 sm:mb-16"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-4 py-3 sm:px-8 sm:py-6 text-base sm:text-lg font-bold rounded-xl shadow-2xl shadow-orange-500/25 transition-all duration-300 hover:scale-105"
              >
                Start Trading Now
                <ArrowRight className="ml-2 h-5 w-5 sm:h-6 sm:w-6" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="bg-white/5 text-white border-white/20 hover:bg-white/10 px-4 py-3 sm:px-8 sm:py-6 text-base sm:text-lg font-bold rounded-xl transition-all duration-300 hover:scale-105"
              >
                Learn More
              </Button>
            </motion.div>
              </div>
              </div>

          {/* Desktop Layout - Two Column */}
          <div className="hidden lg:grid lg:grid-cols-2 gap-12 items-center pt-20 pb-16">
            {/* Left Side - 3D Animated Component */}
            <div className="relative h-[700px] flex items-center justify-center">
              <TradingSphere3D />
              </div>

            {/* Right Side - Content */}
            <div className="text-left">
              {/* Single XXL Bold Heading */}
              <motion.h1
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="text-6xl lg:text-7xl xl:text-8xl font-black leading-tight tracking-tight mb-8"
              >
                <span className="text-white block mb-4">
                  TRADE
                </span>
                <span className="bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent block">
                  LIKE A PRO
                </span>
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-xl md:text-2xl text-gray-300 max-w-2xl mb-12 leading-relaxed"
              >
                Get funded up to <span className="text-orange-500 font-bold">$2.5M</span> with our professional trading platform. 
                Join thousands of successful traders worldwide.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-6 mb-16"
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-6 text-lg font-bold rounded-xl shadow-2xl shadow-orange-500/25 transition-all duration-300 hover:scale-105"
                >
                  Start Trading Now
                  <ArrowRight className="ml-2 h-6 w-6" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="bg-white/5 text-white border-white/20 hover:bg-white/10 px-8 py-6 text-lg font-bold rounded-xl transition-all duration-300 hover:scale-105"
                >
                  Learn More
                </Button>
            </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom wave effect */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-20">
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="url(#wave-gradient)"
          />
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#f97316" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#f97316" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </section>
  )
}
