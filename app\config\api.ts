import { getBaseUrl } from './env';

// API Configuration
export const API_CONFIG = {
  endpoints: {
    login: 'auth/login',
    signup: 'auth/signup',
    adminLogin: 'admin/login',
    order: 'order',
    orderIds: 'order/order_ids',
    accountDetails: 'order/account_detail',
    failOrder: 'order/fail_order',
    failedOrders: 'order/failed_orders',
    myfxbookFetch: 'myfxbook/fetch_account_details',
    // Admin endpoints (all should use 'order/' prefix)
    runningOrders: 'order/running_orders',
    completedOrders: 'order/completed_orders',
    orders: 'order/orders',
    passedOrders: 'order/passed_orders',
    stageTwoOrders: 'order/stage_two_orders',
    liveOrders: 'order/live_orders',
    orderStats: 'order/order_stats',
    userStats: 'order/user_stats',
    users: 'auth/users',
    'verify-2fa': 'admin/verify-2fa'
  } 
} as const;

// Helper function to get only the endpoint path (for secure logging)
export const getEndpointPath = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  if (typeof endpoint === 'string') {
    return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  }
  return `/${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Helper function to get full API URL
export const getApiUrl = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  // Use proxy instead of direct backend URL
  if (typeof endpoint === 'string') {
    // Remove leading slash if present in the endpoint
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `/api/proxy/${cleanEndpoint}`;
  }
  return `/api/proxy/${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Secure fetch functions
export const secureFetch = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('access_token');

  // Don't set Content-Type for FormData - let the browser set it with boundary
  const isFormData = options.body instanceof FormData;

  const headers: Record<string, string> = {
    ...options.headers as Record<string, string>,
    ...(token && { 'Authorization': `Bearer ${token}` })
  };

  // Only set Content-Type for non-FormData requests
  if (!isFormData) {
    headers['Content-Type'] = 'application/json';
  }

  const apiEndpoint = getApiUrl(endpoint);

  // Log only the endpoint path for security
  if (process.env.NODE_ENV !== 'production') {
    // eslint-disable-next-line no-console
    console.log('API Request:', getEndpointPath(endpoint));
  }

  try {
    const response = await fetch(apiEndpoint, {
      ...options,
      headers
    });

    if (!response.ok) {
      // Log only the endpoint path and status, not the full URL
      console.error(`API request failed for ${getEndpointPath(endpoint)}: ${response.status} ${response.statusText}`);
      throw new Error(`API request failed: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error(`API request error for ${getEndpointPath(endpoint)}:`, error);
    throw error;
  }
};

// Direct backend fetch (for server-side only)
export const backendFetch = async (path: string, options: RequestInit = {}) => {
  const baseUrl = getBaseUrl();
  const response = await fetch(`${baseUrl}${path}`, options);

  if (!response.ok) {
    throw new Error('Backend request failed');
  }

  return response;
}; 