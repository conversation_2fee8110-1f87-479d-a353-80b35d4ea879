import { NextRequest } from 'next/server';

export interface SecurityEvent {
  id: string;
  timestamp: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  category: 'authentication' | 'authorization' | 'input_validation' | 'rate_limiting' | 'sql_injection' | 'xss' | 'csrf' | 'file_upload' | 'api_abuse' | 'suspicious_activity';
  message: string;
  details: Record<string, any>;
  ip: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  requestPath?: string;
  requestMethod?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsByCategory: Record<string, number>;
  eventsBySeverity: Record<string, number>;
  recentEvents: SecurityEvent[];
  blockedRequests: number;
  suspiciousIPs: string[];
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private blockedIPs: Set<string> = new Set();
  private suspiciousIPs: Map<string, number> = new Map();
  private rateLimitViolations: Map<string, number> = new Map();
  private maxEvents = 10000; // Keep last 10k events in memory

  /**
   * Log a security event
   */
  logEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date().toISOString()
    };

    this.events.push(securityEvent);
    
    // Keep only the last maxEvents
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Update suspicious IP tracking
    if (event.severity === 'high' || event.severity === 'critical') {
      this.trackSuspiciousIP(event.ip);
    }

    // Log to console for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] ${event.type.toUpperCase()}: ${event.message}`, event.details);
    }

    // In production, you might want to send to external logging service
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(securityEvent);
    }
  }

  /**
   * Track suspicious IP addresses
   */
  private trackSuspiciousIP(ip: string): void {
    const currentCount = this.suspiciousIPs.get(ip) || 0;
    this.suspiciousIPs.set(ip, currentCount + 1);

    // Block IP if it has too many suspicious activities
    if (currentCount >= 10) {
      this.blockedIPs.add(ip);
      this.logEvent({
        type: 'critical',
        category: 'suspicious_activity',
        message: `IP address ${ip} has been blocked due to suspicious activity`,
        details: { reason: 'Multiple suspicious activities detected' },
        ip,
        severity: 'critical'
      });
    }
  }

  /**
   * Check if an IP is blocked
   */
  isIPBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  /**
   * Check if an IP is suspicious
   */
  isIPSuspicious(ip: string): boolean {
    const suspiciousCount = this.suspiciousIPs.get(ip) || 0;
    return suspiciousCount >= 5;
  }

  /**
   * Record rate limit violation
   */
  recordRateLimitViolation(ip: string, endpoint: string): void {
    const key = `${ip}:${endpoint}`;
    const currentCount = this.rateLimitViolations.get(key) || 0;
    this.rateLimitViolations.set(key, currentCount + 1);

    this.logEvent({
      type: 'warning',
      category: 'rate_limiting',
      message: `Rate limit violation for IP ${ip} on endpoint ${endpoint}`,
      details: { endpoint, violationCount: currentCount + 1 },
      ip,
      severity: 'medium'
    });
  }

  /**
   * Analyze request for security threats
   */
  analyzeRequest(request: NextRequest): {
    isThreat: boolean;
    threats: string[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  } {
    const threats: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || '';
    const url = request.url;
    const method = request.method;

    // Check if IP is blocked
    if (this.isIPBlocked(ip)) {
      threats.push('Blocked IP address');
      riskLevel = 'critical';
    }

    // Check if IP is suspicious
    if (this.isIPSuspicious(ip)) {
      threats.push('Suspicious IP address');
      riskLevel = Math.max(riskLevel, 'high');
    }

    // Check for suspicious user agents
    if (this.isSuspiciousUserAgent(userAgent)) {
      threats.push('Suspicious user agent');
      riskLevel = Math.max(riskLevel, 'medium');
    }

    // Check for SQL injection attempts in URL
    if (this.detectSQLInjection(url)) {
      threats.push('Potential SQL injection');
      riskLevel = Math.max(riskLevel, 'high');
    }

    // Check for XSS attempts in URL
    if (this.detectXSS(url)) {
      threats.push('Potential XSS attack');
      riskLevel = Math.max(riskLevel, 'high');
    }

    // Check for path traversal attempts
    if (this.detectPathTraversal(url)) {
      threats.push('Potential path traversal');
      riskLevel = Math.max(riskLevel, 'high');
    }

    // Check for suspicious request patterns
    if (this.isSuspiciousRequestPattern(request)) {
      threats.push('Suspicious request pattern');
      riskLevel = Math.max(riskLevel, 'medium');
    }

    return {
      isThreat: threats.length > 0,
      threats,
      riskLevel
    };
  }

  /**
   * Detect suspicious user agents
   */
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /perl/i,
      /ruby/i,
      /php/i,
      /go-http-client/i,
      /okhttp/i,
      /axios/i,
      /fetch/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Detect SQL injection attempts
   */
  private detectSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\b)/i,
      /(\b(or|and)\b\s+\d+\s*=\s*\d+)/i,
      /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\b.*\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\b)/i,
      /(['"]\s*(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\s*['"])/i,
      /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\b.*['"])/i,
      /(['"].*\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script)\b)/i
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Detect XSS attempts
   */
  private detectXSS(input: string): boolean {
    const xssPatterns = [
      /<script[^>]*>.*<\/script>/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe[^>]*>/i,
      /<object[^>]*>/i,
      /<embed[^>]*>/i,
      /<link[^>]*>/i,
      /<meta[^>]*>/i,
      /<style[^>]*>/i,
      /<form[^>]*>/i,
      /<input[^>]*>/i,
      /<textarea[^>]*>/i,
      /<select[^>]*>/i,
      /<button[^>]*>/i,
      /<a[^>]*>/i,
      /<img[^>]*>/i,
      /<video[^>]*>/i,
      /<audio[^>]*>/i,
      /<source[^>]*>/i,
      /<track[^>]*>/i,
      /<map[^>]*>/i,
      /<area[^>]*>/i,
      /<svg[^>]*>/i,
      /<math[^>]*>/i,
      /<canvas[^>]*>/i,
      /<details[^>]*>/i,
      /<dialog[^>]*>/i,
      /<menu[^>]*>/i,
      /<menuitem[^>]*>/i,
      /<summary[^>]*>/i,
      /<content[^>]*>/i,
      /<element[^>]*>/i,
      /<shadow[^>]*>/i,
      /<template[^>]*>/i,
      /<slot[^>]*>/i,
      /<custom-element[^>]*>/i
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Detect path traversal attempts
   */
  private detectPathTraversal(input: string): boolean {
    const pathTraversalPatterns = [
      /\.{2}\//,
      /\.{2}\\/,
      /%2e%2e%2f/i,
      /%2e%2e%5c/i,
      /\.{2}%2f/i,
      /\.{2}%5c/i,
      /%2e%2e\//i,
      /%2e%2e\\/i
    ];

    return pathTraversalPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Detect suspicious request patterns
   */
  private isSuspiciousRequestPattern(request: NextRequest): boolean {
    const url = request.url;
    const method = request.method;
    const contentType = request.headers.get('content-type') || '';

    // Check for rapid requests to sensitive endpoints
    const sensitiveEndpoints = ['/api/auth/login', '/api/auth/signup', '/api/admin'];
    const isSensitiveEndpoint = sensitiveEndpoints.some(endpoint => url.includes(endpoint));

    // Check for unusual content types
    const unusualContentTypes = ['application/x-www-form-urlencoded', 'multipart/form-data'];
    const hasUnusualContentType = unusualContentTypes.includes(contentType);

    // Check for suspicious HTTP methods
    const suspiciousMethods = ['PUT', 'DELETE', 'PATCH'];
    const hasSuspiciousMethod = suspiciousMethods.includes(method);

    return isSensitiveEndpoint || hasUnusualContentType || hasSuspiciousMethod;
  }

  /**
   * Get security metrics
   */
  getMetrics(): SecurityMetrics {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const recentEvents = this.events.filter(event => 
      new Date(event.timestamp) > oneHourAgo
    );

    const eventsByType = this.events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const eventsByCategory = this.events.reduce((acc, event) => {
      acc[event.category] = (acc[event.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const eventsBySeverity = this.events.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalEvents: this.events.length,
      eventsByType,
      eventsByCategory,
      eventsBySeverity,
      recentEvents,
      blockedRequests: Array.from(this.blockedIPs).length,
      suspiciousIPs: Array.from(this.suspiciousIPs.keys())
    };
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Send event to external logging service (placeholder)
   */
  private sendToExternalLogger(event: SecurityEvent): void {
    // In production, implement integration with external logging services
    // like DataDog, New Relic, AWS CloudWatch, etc.
    
    // Example implementation:
    // fetch('https://api.logging-service.com/events', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(event)
    // });
  }

  /**
   * Clear old events (for memory management)
   */
  clearOldEvents(olderThanHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    this.events = this.events.filter(event => 
      new Date(event.timestamp) > cutoffTime
    );
  }

  /**
   * Export events for analysis
   */
  exportEvents(): SecurityEvent[] {
    return [...this.events];
  }
}

// Create singleton instance
export const securityMonitor = new SecurityMonitor();

// Export convenience functions
export const logSecurityEvent = (
  type: SecurityEvent['type'],
  category: SecurityEvent['category'],
  message: string,
  details: Record<string, any>,
  request: NextRequest,
  severity: SecurityEvent['severity'] = 'medium'
) => {
  securityMonitor.logEvent({
    type,
    category,
    message,
    details,
    ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
    userAgent: request.headers.get('user-agent'),
    requestPath: request.url,
    requestMethod: request.method,
    severity
  });
};

export const analyzeRequest = (request: NextRequest) => {
  return securityMonitor.analyzeRequest(request);
};

export const getSecurityMetrics = () => {
  return securityMonitor.getMetrics();
}; 