"use client";
import { useState, FormEvent, useEffect } from "react";
import { Navbar } from "../component/navbar";
import { motion } from "framer-motion";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useRouter } from 'next/navigation';
import { Bitcoin, DollarSign } from 'lucide-react';
import { getApiUrl, getEndpointPath } from "../config/api";
import secureStorageAPI from '@/app/lib/secureStorage';

const FloatingIcons = () => {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1000,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            x: Math.random() * dimensions.width,
            y: Math.random() * dimensions.height,
            scale: 0.5 + Math.random() * 0.5,
            opacity: 0.3 + Math.random() * 0.4
          }}
          animate={{
            x: [null, Math.random() * dimensions.width],
            y: [null, Math.random() * dimensions.height],
            rotate: [0, 360]
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute text-gray-600"
        >
          {i % 2 === 0 ? <Bitcoin size={24} /> : <DollarSign size={24} />}
        </motion.div>
      ))}
    </div>
  );
};

export default function VerifyEmail() {
  const router = useRouter();
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  useEffect(() => {
    // Get email from localStorage
    const email = secureStorageAPI.getItem('verification_email');
    if (!email) {
      toast.error('No email found for verification');
      router.push('/signup');
      return;
    }
    setUserEmail(email);
  }, [router]);

  const handleChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.querySelector(`input[name='code-${index + 1}']`) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const code = verificationCode.join('');
    
    try {
      // Construct URL with query parameters
      const url = getApiUrl(`auth/verify-email?email=${encodeURIComponent(userEmail)}&code=${encodeURIComponent(code)}`);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        toast.success('Email verified successfully!');
        // Clear the stored email
        secureStorageAPI.removeItem('verification_email');
        setTimeout(() => {
          router.push('/sigin');
        }, 2000);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Invalid verification code. Please try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
      console.error('Verification error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    try {
      // Add your resend code endpoint here
      toast.info('Verification code resent to your email!');
    } catch (error) {
      toast.error('Failed to resend verification code');
    }
  };

  return (
    <>
      <Navbar />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900 p-4 md:p-6 lg:p-8 relative">
        <FloatingIcons />
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-br from-gray-900 to-black rounded-2xl shadow-2xl p-6 md:p-8 lg:p-10 w-full max-w-md mt-20 mx-auto border border-gray-800"
        >
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 mb-8 text-center"
          >
            Verify Your Email
          </motion.h1>
          <p className="text-gray-400 text-center mb-2">
            Please enter the 6-digit verification code sent to:
          </p>
          <p className="text-orange-400 text-center mb-8 font-semibold">
            {userEmail}
          </p>
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="flex justify-center gap-2">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  type="text"
                  name={`code-${index}`}
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleChange(index, e.target.value)}
                  className="w-12 h-12 text-center text-xl font-bold rounded-lg bg-black/50 text-white border border-gray-700 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300"
                  required
                />
              ))}
            </div>
            <motion.div
              whileHover={{ scale: isLoading ? 1 : 1.02 }}
              whileTap={{ scale: isLoading ? 1 : 0.98 }}
            >
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-orange-400 to-orange-500 text-white font-bold py-4 rounded-lg hover:from-orange-500 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/25 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Verifying...
                  </>
                ) : (
                  'Verify Email'
                )}
              </button>
            </motion.div>
          </form>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-center mt-6"
          >
            <p className="text-gray-400">
              Didn't receive the code?{" "}
              <button 
                onClick={handleResendCode}
                className="text-orange-400 hover:text-orange-500 hover:underline transition-colors duration-300"
              >
                Resend
              </button>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}





