'use client'

import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, Award, DollarSign, TrendingUp, Play, Pause, Star, Zap, Target, Crown, Trophy, Users, ArrowRight } from 'lucide-react'

export function EnhancedPayoutSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  const y = useTransform(scrollYProgress, [0, 1], [100, -100])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0])

  const payoutCertificates = [
    {
      name: "Kabiru Sani",
      image: "/payout/Kabiru Sani payout certificate-02.jpg",
      amount: "$12,450",
      profit: "+24.5%",
      date: "Recent",
      badge: "Horizon Trader",
      rank: "Diamond"
    },
    {
      name: "Farhad Mehrabi",
      image: "/payout/Farhad Mehrabi payout certificate-02.jpg",
      amount: "$8,320",
      profit: "+18.2%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Musa Okello",
      image: "/payout/Musa Okello payout certificate-02-02.jpg",
      amount: "$15,780",
      profit: "+31.4%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Tariq Anwar",
      image: "/payout/Tariq Anwar payout certificate-02.jpg",
      amount: "$6,940",
      profit: "+15.8%",
      date: "Recent",
      badge: "Horizon Trader",
      rank: "Gold"
    },
    {
      name: "Ajmal Haidar",
      image: "/payout/Ajmal Haidar payout certificate-02.jpg",
      amount: "$9,650",
      profit: "+22.1%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Emre Kaya",
      image: "/payout/Emre Kaya payout certificate-02.jpg",
      amount: "$11,200",
      profit: "+28.7%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Omar Benyamina",
      image: "/payout/Omar Benyamina payout certificate-02.jpg",
      amount: "$7,890",
      profit: "+19.3%",
      date: "Recent",
      badge: "Horizon Trader",
      rank: "Gold"
    },
    {
      name: "Faisal Khan",
      image: "/payout/Faisal Khan payout certificate-02.jpg",
      amount: "$13,450",
      profit: "+26.8%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    },
    {
      name: "Abubakar Nsubuga",
      image: "/payout/Abubakar Nsubuga payout certificate-02.jpg",
      amount: "$10,120",
      profit: "+21.4%",
      date: "Recent",
      badge: "Pro Trader",
      rank: "Platinum"
    },
    {
      name: "Hassan Dogo",
      image: "/payout/Hassan Dogo payout certificate-02.jpg",
      amount: "$8,760",
      profit: "+17.9%",
      date: "Recent",
      badge: "Horizon Trader",
      rank: "Gold"
    },
    {
      name: "Ismail Geddi",
      image: "/payout/Ismail Geddi  payout certificate-02.jpg",
      amount: "$14,230",
      profit: "+29.2%",
      date: "Recent",
      badge: "Master Trader",
      rank: "Diamond"
    }
  ]

  // Auto-sliding functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === payoutCertificates.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, payoutCertificates.length])

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === payoutCertificates.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? payoutCertificates.length - 1 : prevIndex - 1
    )
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  const getRankColor = (rank: string) => {
    switch (rank) {
      case 'Diamond': return 'from-purple-500 to-pink-500'
      case 'Platinum': return 'from-gray-400 to-gray-600'
      case 'Gold': return 'from-yellow-400 to-orange-500'
      default: return 'from-blue-500 to-cyan-500'
    }
  }

  return (
    <section ref={containerRef} className="py-24 bg-gradient-to-b from-[#0A0F1C] via-[#151B2C] to-[#0A0F1C] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-[#FF6B2C] to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-tl from-[#4A90E2] to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-[#FF6B2C] to-[#4A90E2] rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header Section */}
        <motion.div
          style={{ y, opacity }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="mb-6"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex items-center gap-1 bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] px-4 py-2 rounded-full">
                <Trophy className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-semibold">SUCCESS STORIES</span>
              </div>
            </div>
            
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-[#E8F4FD] to-white bg-clip-text text-transparent">
              <span className="text-2xl sm:text-4xl md:text-6xl">Horizon Payout Certificates</span>
            </h2>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-[#8B9BB8] text-sm sm:text-base md:text-xl max-w-4xl mx-auto leading-relaxed"
          >
            Witness the extraordinary success of our Horizon traders through authentic payout certificates. 
            Each certificate represents real profits earned through our advanced trading platform.
          </motion.p>
        </motion.div>

        {/* Professional Marquee Certificate Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="max-w-7xl mx-auto my-20"
        >
          <div className="bg-gradient-to-br from-[#1A1F2E] to-[#2A2F3E] rounded-3xl p-8 md:p-12 border border-[#3A3F4E] shadow-2xl">
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                <span className="text-xl sm:text-3xl md:text-4xl">All Payout Certificates</span>
              </h3>
              <p className="text-[#8B9BB8] text-xs sm:text-base">
                <span className="text-xs sm:text-base">Real trading success stories from our community</span>
              </p>
            </div>

            <div className="space-y-16">
              {/* First Row: left-to-right */}
              <div className="overflow-hidden w-full">
                <motion.div
                  className="flex gap-8"
                  animate={{ x: [0, '-50%'] }}
                  transition={{
                    repeat: Infinity,
                    repeatType: 'loop',
                    duration: 40,
                    ease: 'linear',
                  }}
                  style={{ width: 'max-content' }}
                >
                  {[...payoutCertificates, ...payoutCertificates].map((certificate, idx) => (
                    <motion.div
                      key={`row1-${idx}`}
                      whileHover={{ y: -8, scale: 1.02 }}
                      className="relative cursor-pointer rounded-2xl overflow-hidden border border-[#3A3F4E] hover:border-[#FF6B2C] transition-all duration-500 shadow-xl min-w-[320px] max-w-[400px] w-[25vw] bg-gradient-to-br from-[#1A1F2E] to-[#2A2F3E] group"
                    >
                      <div className="aspect-[4/3] relative">
                  <Image
                          src={certificate.image}
                          alt={`${certificate.name} Certificate`}
                    fill
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                          loading="lazy"
                  />
                        <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300" />
                  
                        {/* Overlay Content */}
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="bg-gradient-to-r from-black/80 to-black/60 backdrop-blur-sm rounded-xl p-4">
                            <div className="flex items-center justify-between mb-2">
                              <p className="text-white text-lg font-semibold truncate">
                                <span className="text-base sm:text-lg">{certificate.name}</span>
                              </p>
                              <div className="flex items-center gap-1 bg-[#FF6B2C]/20 px-2 py-1 rounded-full">
                                <Star className="w-3 h-3 text-[#FF6B2C]" />
                                <span className="text-[#FF6B2C] text-xs sm:text-sm font-semibold">{certificate.rank}</span>
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <p className="text-[#FF6B2C] text-xl font-bold">
                                <span className="text-base sm:text-xl">{certificate.amount}</span>
                              </p>
                              <p className="text-[#4A90E2] text-sm font-semibold">
                                <span className="text-xs sm:text-sm">{certificate.profit}</span>
                              </p>
                            </div>
                          </div>
                        </div>
            </div>
                </motion.div>
                  ))}
                </motion.div>
              </div>
              
              {/* Second Row: right-to-left (antiparallel) */}
              <div className="overflow-hidden w-full">
        <motion.div
                  className="flex gap-8"
                  animate={{ x: ['-50%', 0] }}
                  transition={{
                    repeat: Infinity,
                    repeatType: 'loop',
                    duration: 40,
                    ease: 'linear',
                  }}
                  style={{ width: 'max-content' }}
        >
                  {[...payoutCertificates, ...payoutCertificates].map((certificate, idx) => (
        <motion.div
                      key={`row2-${idx}`}
                      whileHover={{ y: -8, scale: 1.02 }}
                      className="relative cursor-pointer rounded-2xl overflow-hidden border border-[#3A3F4E] hover:border-[#4A90E2] transition-all duration-500 shadow-xl min-w-[320px] max-w-[400px] w-[25vw] bg-gradient-to-br from-[#1A1F2E] to-[#2A2F3E] group"
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={certificate.image}
                    alt={`${certificate.name} Certificate`}
                    fill
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                        <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300" />
                  
                        {/* Overlay Content */}
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="bg-gradient-to-r from-black/80 to-black/60 backdrop-blur-sm rounded-xl p-4">
                            <div className="flex items-center justify-between mb-2">
                              <p className="text-white text-lg font-semibold truncate">
                        <span className="text-base sm:text-lg">{certificate.name}</span>
                      </p>
                              <div className="flex items-center gap-1 bg-[#4A90E2]/20 px-2 py-1 rounded-full">
                                <Award className="w-3 h-3 text-[#4A90E2]" />
                                <span className="text-[#4A90E2] text-xs sm:text-sm font-semibold">{certificate.rank}</span>
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <p className="text-[#FF6B2C] text-xl font-bold">
                        <span className="text-base sm:text-xl">{certificate.amount}</span>
                      </p>
                              <p className="text-[#4A90E2] text-sm font-semibold">
                                <span className="text-xs sm:text-sm">{certificate.profit}</span>
                              </p>
                            </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
              </div>
            </div>
            </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8 }}
          className="mt-16 text-center"
        >
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-[#FF6B2C] to-[#FF8B3D] hover:from-[#FF8B3D] hover:to-[#FF6B2C] text-white px-4 py-2 sm:px-8 sm:py-4 rounded-xl text-base sm:text-lg font-semibold transition-all duration-300 shadow-lg shadow-[#FF6B2C]/20 hover:shadow-[#FF6B2C]/40"
          >
            Join Our Horizon Traders
            <ArrowRight className="w-5 h-5" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
} 