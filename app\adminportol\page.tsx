"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { AdminOverview } from "@/components/admin-overview"
import { AdminTables } from "@/app/components/admin-tables"
import { LogOut, Settings, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import AdminSidebar from "./sidebar"
import secureStorageAPI from '@/app/lib/secureStorage'

export default function AdminDashboard() {
  const router = useRouter()
  const [selectedSection, setSelectedSection] = useState<"orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "certificates" | "analytics" | "reports" | null>("orders")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleLogout = () => {
    secureStorageAPI.removeItem("admin-token")
    router.push("/login")
  }

  if (!mounted) {
    return <div className="min-h-screen bg-[#0a1929] flex items-center justify-center">
      <div className="animate-pulse text-white">Loading...</div>
    </div>
  }

  return (
    <div className="min-h-screen bg-[#0a1929] flex">
      <AdminSidebar />
      <div className="flex-1">
      {/* Header */}
      <div className="border-b border-gray-800/50 bg-[#0d2339]/90 backdrop-blur-sm">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <motion.h1
            className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            Admin Dashboard
          </motion.h1>

          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full w-10 h-10">
                  <User className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push("/settings")}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="container mx-auto py-6 px-4 relative z-10">
        {/* Admin Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-gradient-to-br from-[#0d2339]/90 to-[#132f4c]/90 rounded-xl border border-gray-800/50 p-6 backdrop-blur-sm shadow-xl"
        >
          <AdminOverview onSelectSection={setSelectedSection} selectedSection={selectedSection} />
        </motion.div>

        {/* Admin Tables */}
        <motion.div
          className="mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }} 
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <AdminTables selectedSection={selectedSection} />
        </motion.div>
        </div>
      </div>
    </div>
  )
}
