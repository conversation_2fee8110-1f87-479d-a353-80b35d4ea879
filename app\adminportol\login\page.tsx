"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { getApiUrl } from "../../config/api"
import secureStorageAPI from '@/app/lib/secureStorage'

export default function AdminLoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({ username: "", password: "" })
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)
    try {
      const res = await fetch(getApiUrl('adminLogin'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: formData.username, password: formData.password })
      })
      const data = await res.json()
      if (data.two_factor_required && data.two_factor_token) {
        secureStorageAPI.setItem("two_factor_token", data.two_factor_token)
        router.push("/adminportol/verify-2fa")
      } else if (data.access_token) {
        secureStorageAPI.setItem("admin-token", data.access_token)
        router.push("/adminportol")
      } else {
        setError(data.message || "Login failed")
      }
    } catch (err) {
      setError("Login failed. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#0a1929]">
      <form onSubmit={handleSubmit} className="bg-[#0d2339]/90 p-8 rounded-xl border border-gray-800/50 w-full max-w-md space-y-6">
        <h1 className="text-2xl font-bold text-center text-orange-400">Admin Login</h1>
        <input
          type="text"
          name="username"
          placeholder="Username"
          value={formData.username}
          onChange={handleChange}
          required
          className="w-full p-2 rounded bg-white/10 text-white border border-gray-700"
        />
        <input
          type="password"
          name="password"
          placeholder="Password"
          value={formData.password}
          onChange={handleChange}
          required
          className="w-full p-2 rounded bg-white/10 text-white border border-gray-700"
        />
        {error && <div className="text-red-400 text-center">{error}</div>}
        <button type="submit" disabled={loading} className="w-full bg-orange-500 hover:bg-orange-600 text-white p-2 rounded">
          {loading ? "Logging in..." : "Login"}
        </button>
      </form>
    </div>
  )
} 