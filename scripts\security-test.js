#!/usr/bin/env node

/**
 * Comprehensive Security Testing Script
 * Tests all security measures implemented in the application
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

class SecurityTester {
  constructor() {
    this.baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000';
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runAllTests() {
    console.log('🔒 Starting Comprehensive Security Tests...\n');
    
    await this.testSecurityHeaders();
    await this.testCSRFProtection();
    await this.testRateLimiting();
    await this.testInputValidation();
    await this.testAuthentication();
    await this.testXSSProtection();
    await this.testSQLInjectionProtection();
    await this.testFileUploadSecurity();
    await this.testPathTraversalProtection();
    await this.testContentSecurityPolicy();
    await this.testSecureStorage();
    await this.testAPIEndpoints();
    
    this.printResults();
  }

  async testSecurityHeaders() {
    console.log('Testing Security Headers...');
    
    try {
      const response = await this.makeRequest('/');
      const headers = response.headers;
      
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'strict-transport-security',
        'content-security-policy',
        'referrer-policy'
      ];
      
      for (const header of requiredHeaders) {
        if (headers[header]) {
          this.addResult('PASS', `Security Header: ${header}`, 'Header is present');
        } else {
          this.addResult('FAIL', `Security Header: ${header}`, 'Header is missing');
        }
      }
    } catch (error) {
      this.addResult('FAIL', 'Security Headers', error.message);
    }
  }

  async testCSRFProtection() {
    console.log('Testing CSRF Protection...');
    
    try {
      // Test without CSRF token
      const responseWithoutToken = await this.makeRequest('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'username=test&password=test'
      });
      
      if (responseWithoutToken.status === 403) {
        this.addResult('PASS', 'CSRF Protection', 'Request without token blocked');
      } else {
        this.addResult('FAIL', 'CSRF Protection', 'Request without token should be blocked');
      }
      
      // Test with invalid CSRF token
      const responseWithInvalidToken = await this.makeRequest('/api/auth/login', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/x-www-form-urlencoded',
          'x-csrf-token': 'invalid-token'
        },
        body: 'username=test&password=test'
      });
      
      if (responseWithInvalidToken.status === 403) {
        this.addResult('PASS', 'CSRF Protection', 'Request with invalid token blocked');
      } else {
        this.addResult('FAIL', 'CSRF Protection', 'Request with invalid token should be blocked');
      }
    } catch (error) {
      this.addResult('FAIL', 'CSRF Protection', error.message);
    }
  }

  async testRateLimiting() {
    console.log('Testing Rate Limiting...');
    
    try {
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(this.makeRequest('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: 'username=test&password=test'
        }));
      }
      
      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      if (rateLimitedResponses.length > 0) {
        this.addResult('PASS', 'Rate Limiting', `${rateLimitedResponses.length} requests were rate limited`);
      } else {
        this.addResult('FAIL', 'Rate Limiting', 'No requests were rate limited');
      }
    } catch (error) {
      this.addResult('FAIL', 'Rate Limiting', error.message);
    }
  }

  async testInputValidation() {
    console.log('Testing Input Validation...');
    
    const testCases = [
      {
        name: 'SQL Injection',
        input: "'; DROP TABLE users; --",
        expectedBlocked: true
      },
      {
        name: 'XSS Attack',
        input: '<script>alert("xss")</script>',
        expectedBlocked: true
      },
      {
        name: 'Path Traversal',
        input: '../../../etc/passwd',
        expectedBlocked: true
      },
      {
        name: 'Valid Input',
        input: '<EMAIL>',
        expectedBlocked: false
      }
    ];
    
    for (const testCase of testCases) {
      try {
        const response = await this.makeRequest('/api/auth/signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testCase.input,
            password: 'TestPassword123!',
            name: 'Test User'
          })
        });
        
        if (testCase.expectedBlocked && response.status === 400) {
          this.addResult('PASS', `Input Validation: ${testCase.name}`, 'Malicious input blocked');
        } else if (!testCase.expectedBlocked && response.status !== 400) {
          this.addResult('PASS', `Input Validation: ${testCase.name}`, 'Valid input accepted');
        } else {
          this.addResult('FAIL', `Input Validation: ${testCase.name}`, 
            testCase.expectedBlocked ? 'Malicious input should be blocked' : 'Valid input should be accepted');
        }
      } catch (error) {
        this.addResult('FAIL', `Input Validation: ${testCase.name}`, error.message);
      }
    }
  }

  async testAuthentication() {
    console.log('Testing Authentication...');
    
    try {
      // Test protected endpoint without authentication
      const response = await this.makeRequest('/api/security/metrics');
      
      if (response.status === 401) {
        this.addResult('PASS', 'Authentication', 'Protected endpoint requires authentication');
      } else {
        this.addResult('FAIL', 'Authentication', 'Protected endpoint should require authentication');
      }
    } catch (error) {
      this.addResult('FAIL', 'Authentication', error.message);
    }
  }

  async testXSSProtection() {
    console.log('Testing XSS Protection...');
    
    const xssPayloads = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src="x" onerror="alert(\'xss\')">',
      '<iframe src="javascript:alert(\'xss\')"></iframe>',
      '<svg onload="alert(\'xss\')"></svg>'
    ];
    
    for (const payload of xssPayloads) {
      try {
        const response = await this.makeRequest('/api/auth/signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: payload,
            password: 'TestPassword123!',
            name: payload
          })
        });
        
        if (response.status === 400) {
          this.addResult('PASS', `XSS Protection: ${payload.substring(0, 20)}...`, 'XSS payload blocked');
        } else {
          this.addResult('FAIL', `XSS Protection: ${payload.substring(0, 20)}...`, 'XSS payload should be blocked');
        }
      } catch (error) {
        this.addResult('FAIL', `XSS Protection: ${payload.substring(0, 20)}...`, error.message);
      }
    }
  }

  async testSQLInjectionProtection() {
    console.log('Testing SQL Injection Protection...');
    
    const sqlPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "1' OR '1' = '1' --"
    ];
    
    for (const payload of sqlPayloads) {
      try {
        const response = await this.makeRequest('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: `username=${encodeURIComponent(payload)}&password=test`
        });
        
        if (response.status === 400 || response.status === 403) {
          this.addResult('PASS', `SQL Injection Protection: ${payload.substring(0, 20)}...`, 'SQL injection blocked');
        } else {
          this.addResult('FAIL', `SQL Injection Protection: ${payload.substring(0, 20)}...`, 'SQL injection should be blocked');
        }
      } catch (error) {
        this.addResult('FAIL', `SQL Injection Protection: ${payload.substring(0, 20)}...`, error.message);
      }
    }
  }

  async testFileUploadSecurity() {
    console.log('Testing File Upload Security...');
    
    const maliciousFiles = [
      { name: 'test.php', content: '<?php echo "malicious"; ?>', type: 'application/x-php' },
      { name: 'test.js', content: 'alert("malicious")', type: 'application/javascript' },
      { name: 'test.exe', content: 'malicious binary', type: 'application/x-executable' }
    ];
    
    for (const file of maliciousFiles) {
      try {
        const response = await this.makeRequest('/api/upload', {
          method: 'POST',
          headers: { 'Content-Type': 'multipart/form-data' },
          body: `file=${encodeURIComponent(JSON.stringify(file))}`
        });
        
        if (response.status === 400 || response.status === 403) {
          this.addResult('PASS', `File Upload Security: ${file.name}`, 'Malicious file blocked');
        } else {
          this.addResult('FAIL', `File Upload Security: ${file.name}`, 'Malicious file should be blocked');
        }
      } catch (error) {
        this.addResult('FAIL', `File Upload Security: ${file.name}`, error.message);
      }
    }
  }

  async testPathTraversalProtection() {
    console.log('Testing Path Traversal Protection...');
    
    const pathTraversalPayloads = [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\config\\sam',
      '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
      '....//....//....//etc/passwd'
    ];
    
    for (const payload of pathTraversalPayloads) {
      try {
        const response = await this.makeRequest(`/api/files/${encodeURIComponent(payload)}`);
        
        if (response.status === 400 || response.status === 403) {
          this.addResult('PASS', `Path Traversal Protection: ${payload.substring(0, 20)}...`, 'Path traversal blocked');
        } else {
          this.addResult('FAIL', `Path Traversal Protection: ${payload.substring(0, 20)}...`, 'Path traversal should be blocked');
        }
      } catch (error) {
        this.addResult('FAIL', `Path Traversal Protection: ${payload.substring(0, 20)}...`, error.message);
      }
    }
  }

  async testContentSecurityPolicy() {
    console.log('Testing Content Security Policy...');
    
    try {
      const response = await this.makeRequest('/');
      const csp = response.headers['content-security-policy'];
      
      if (csp) {
        const requiredDirectives = ['default-src', 'script-src', 'style-src'];
        const missingDirectives = requiredDirectives.filter(directive => !csp.includes(directive));
        
        if (missingDirectives.length === 0) {
          this.addResult('PASS', 'Content Security Policy', 'CSP properly configured');
        } else {
          this.addResult('FAIL', 'Content Security Policy', `Missing directives: ${missingDirectives.join(', ')}`);
        }
      } else {
        this.addResult('FAIL', 'Content Security Policy', 'CSP header missing');
      }
    } catch (error) {
      this.addResult('FAIL', 'Content Security Policy', error.message);
    }
  }

  async testSecureStorage() {
    console.log('Testing Secure Storage...');
    
    try {
      // Test localStorage replacement
      const testData = { sensitive: 'data' };
      
      // This would be tested in a browser environment
      // For now, we'll just check if the secure storage module exists
      const secureStoragePath = path.join(process.cwd(), 'app/lib/secureStorage.ts');
      
      if (fs.existsSync(secureStoragePath)) {
        this.addResult('PASS', 'Secure Storage', 'Secure storage module exists');
      } else {
        this.addResult('FAIL', 'Secure Storage', 'Secure storage module missing');
      }
    } catch (error) {
      this.addResult('FAIL', 'Secure Storage', error.message);
    }
  }

  async testAPIEndpoints() {
    console.log('Testing API Endpoints...');
    
    const endpoints = [
      '/api/auth/login',
      '/api/auth/signup',
      '/api/proxy/test',
      '/api/security/metrics',
      '/api/security/events'
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await this.makeRequest(endpoint, { method: 'OPTIONS' });
        
        if (response.status === 200 || response.status === 405) {
          this.addResult('PASS', `API Endpoint: ${endpoint}`, 'Endpoint accessible');
        } else {
          this.addResult('FAIL', `API Endpoint: ${endpoint}`, `Unexpected status: ${response.status}`);
        }
      } catch (error) {
        this.addResult('FAIL', `API Endpoint: ${endpoint}`, error.message);
      }
    }
  }

  async makeRequest(path, options = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const requestOptions = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'SecurityTester/1.0',
          ...options.headers
        }
      };
      
      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        });
      });
      
      req.on('error', reject);
      
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  addResult(status, test, message) {
    const result = { status, test, message, timestamp: new Date().toISOString() };
    this.results.tests.push(result);
    
    if (status === 'PASS') {
      this.results.passed++;
      console.log(`✅ ${test}: ${message}`);
    } else {
      this.results.failed++;
      console.log(`❌ ${test}: ${message}`);
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🔒 SECURITY TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.results.tests.length}`);
    console.log(`Passed: ${this.results.passed} ✅`);
    console.log(`Failed: ${this.results.failed} ❌`);
    console.log(`Success Rate: ${((this.results.passed / this.results.tests.length) * 100).toFixed(1)}%`);
    
    if (this.results.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.tests
        .filter(test => test.status === 'FAIL')
        .forEach(test => {
          console.log(`  - ${test.test}: ${test.message}`);
        });
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Save results to file
    const resultsPath = path.join(process.cwd(), 'security-test-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(this.results, null, 2));
    console.log(`Results saved to: ${resultsPath}`);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SecurityTester; 