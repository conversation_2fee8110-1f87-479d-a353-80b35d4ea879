import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { validateRequest, getSecurityHeaders, RateLimiter, logSecurityEvent } from './app/lib/security'

export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname

  // Security: Validate request
  const requestValidation = validateRequest(request)
  if (!requestValidation.valid) {
    logSecurityEvent('Request blocked', { reason: requestValidation.reason, path }, 'high')
    return new NextResponse('Forbidden', { status: 403 })
  }

  // Security: Rate limiting
  const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const rateLimit = RateLimiter.checkRateLimit(clientIP)
  
  if (!rateLimit.allowed) {
    logSecurityEvent('Rate limit exceeded', { clientIP, path }, 'medium')
    return new NextResponse('Too Many Requests', { 
      status: 429,
      headers: {
        'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString()
      }
    })
  }

  // Define public paths that don't require authentication
  const isPublicPath = path === '/login' || path === '/signup' || path === '/forgot-password' || path === '/reset-password' || path === '/verify-email'

  // Get the token from the cookies
  const token = request.cookies.get('admin-token')?.value || ''

  // If the path starts with /dashboard/admin
  if (path.startsWith('/dashboard/admin')) {
    // If there's no token and we're not on a public path, redirect to login
    if (!token) {
      logSecurityEvent('Unauthorized admin access attempt', { path, clientIP }, 'medium')
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // If we're on a public path and have a token, redirect to admin dashboard
  if (isPublicPath && token) {
    return NextResponse.redirect(new URL('/dashboard/admin', request.url))
  }

  // Create response
  const response = NextResponse.next()

  // Add security headers to all responses
  const securityHeaders = getSecurityHeaders()
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', '100')
  response.headers.set('X-RateLimit-Remaining', rateLimit.remainingAttempts.toString())
  response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString())

  return response
}

// Configure the paths that middleware will run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
} 