"use client"

import { motion } from "framer-motion"

export function AccountOptions() {
  const accounts = [
    {
      size: "$10,000",
      price: "$97",
      features: [
        "Maximum Daily Loss: $500",
        "Maximum Loss: $1,000",
        "Profit Target: $1,000",
        "Profit Split: Up to 90%"
      ]
    },
    {
      size: "$50,000",
      price: "$297",
      popular: true,
      features: [
        "Maximum Daily Loss: $2,500",
        "Maximum Loss: $5,000",
        "Profit Target: $5,000",
        "Profit Split: Up to 90%"
      ]
    },
    {
      size: "$200,000",
      price: "$997",
      features: [
        "Maximum Daily Loss: $10,000",
        "Maximum Loss: $20,000",
        "Profit Target: $20,000",
        "Profit Split: Up to 90%"
      ]
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-[#0A0F1C] to-black">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-16">
          <span className="text-2xl sm:text-4xl text-white">Choose Your </span>
          <span className="text-2xl sm:text-4xl bg-gradient-to-r from-orange-400 to-blue-500 bg-clip-text text-transparent">
            Account Size
          </span>
        </h2>

        <div className="grid md:grid-cols-3 gap-8">
          {accounts.map((account) => (
            <motion.div
              key={account.size}
              className={`relative bg-white/5 rounded-2xl p-8 border ${
                account.popular ? 'border-orange-500' : 'border-white/10'
              }`}
            >
              <div className="text-lg sm:text-2xl font-bold text-white mb-2">{account.size}</div>
              <div className="text-base sm:text-lg text-orange-400 mb-4">{account.price}</div>
              <ul className="space-y-2">
                {account.features.map((feature, idx) => (
                  <li key={idx} className="text-xs sm:text-sm text-blue-100/80">{feature}</li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
} 